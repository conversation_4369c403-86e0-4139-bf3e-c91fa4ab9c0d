import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  MapPin,
  AlertCircle,
  CheckCircle,
  Clock,
  Navigation,
} from "lucide-react";

const LocationTest: React.FC = () => {
  const [testStatus, setTestStatus] = useState<
    "idle" | "testing" | "success" | "error"
  >("idle");
  const [location, setLocation] = useState<{ lat: number; lng: number } | null>(
    null
  );
  const [error, setError] = useState<string | null>(null);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [manualLat, setManualLat] = useState<string>("");
  const [manualLng, setManualLng] = useState<string>("");
  const [showManualInput, setShowManualInput] = useState<boolean>(false);

  const testGeolocation = () => {
    setTestStatus("testing");
    setError(null);
    setLocation(null);
    setAccuracy(null);

    console.log("🧪 LocationTest: Starting geolocation test...");

    if (!navigator.geolocation) {
      setTestStatus("error");
      setError("Geolocation is not supported by this browser");
      console.log("❌ LocationTest: Geolocation not supported");
      return;
    }

    // Test geolocation with high accuracy
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setLocation(coords);
        setAccuracy(position.coords.accuracy);
        setTestStatus("success");
        console.log("✅ LocationTest: Success!", coords);
        console.log(
          "📍 LocationTest: Accuracy:",
          position.coords.accuracy,
          "meters"
        );
        console.log(
          "🕐 LocationTest: Timestamp:",
          new Date(position.timestamp)
        );
      },
      (error) => {
        setTestStatus("error");
        let errorMessage = "";

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Location access denied by user. Please allow location access and try again.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage =
              "Location information is unavailable. Check your device settings.";
            break;
          case error.TIMEOUT:
            errorMessage =
              "Location request timed out. Try again or check your connection.";
            break;
          default:
            errorMessage =
              "An unknown error occurred while retrieving location.";
            break;
        }

        setError(errorMessage);
        console.log("❌ LocationTest: Error code:", error.code);
        console.log("❌ LocationTest: Error message:", error.message);
        console.log("❌ LocationTest: Detailed error:", errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0, // Force fresh location
      }
    );
  };

  const setManualLocation = () => {
    const lat = parseFloat(manualLat);
    const lng = parseFloat(manualLng);

    if (isNaN(lat) || isNaN(lng)) {
      setError("Please enter valid latitude and longitude values");
      setTestStatus("error");
      return;
    }

    if (lat < -90 || lat > 90) {
      setError("Latitude must be between -90 and 90");
      setTestStatus("error");
      return;
    }

    if (lng < -180 || lng > 180) {
      setError("Longitude must be between -180 and 180");
      setTestStatus("error");
      return;
    }

    const manualCoords = { lat, lng };
    setLocation(manualCoords);
    setTestStatus("success");
    setAccuracy(null); // No accuracy for manual input
    setError(null);

    console.log("📍 LocationTest: Manual location set:", manualCoords);

    // Trigger a custom event to notify other components
    window.dispatchEvent(
      new CustomEvent("manualLocationSet", {
        detail: manualCoords,
      })
    );
  };

  const getStatusIcon = () => {
    switch (testStatus) {
      case "testing":
        return <Clock className="h-4 w-4 text-yellow-600 animate-spin" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <MapPin className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch (testStatus) {
      case "testing":
        return "border-yellow-200 bg-yellow-50";
      case "success":
        return "border-green-200 bg-green-50";
      case "error":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  return (
    <Card className={`${getStatusColor()}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Location Test
        </CardTitle>
        <CardDescription>
          Test your browser's geolocation capabilities
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button
          onClick={testGeolocation}
          disabled={testStatus === "testing"}
          className="w-full"
        >
          {testStatus === "testing"
            ? "Testing Location..."
            : "Test My Location"}
        </Button>

        {testStatus === "testing" && (
          <div className="p-3 bg-yellow-100 rounded-lg border border-yellow-200">
            <p className="text-sm text-yellow-800">
              Testing geolocation... This may take up to 15 seconds.
            </p>
            <p className="text-xs text-yellow-600 mt-1">
              Please allow location access when prompted by your browser.
            </p>
          </div>
        )}

        {testStatus === "success" && location && (
          <div className="p-3 bg-green-100 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                Location Detected!
              </span>
            </div>
            <div className="space-y-1 text-xs text-green-700">
              <p>
                <strong>Latitude:</strong> {location.lat.toFixed(6)}
              </p>
              <p>
                <strong>Longitude:</strong> {location.lng.toFixed(6)}
              </p>
              {accuracy && (
                <p>
                  <strong>Accuracy:</strong> ±{Math.round(accuracy)} meters
                </p>
              )}
            </div>
            <Badge
              variant="outline"
              className="mt-2 text-green-700 border-green-300"
            >
              Geolocation Working
            </Badge>
          </div>
        )}

        {testStatus === "error" && error && (
          <div className="p-3 bg-red-100 rounded-lg border border-red-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">
                Location Test Failed
              </span>
            </div>
            <p className="text-xs text-red-700">{error}</p>
            <Badge variant="destructive" className="mt-2">
              Geolocation Issue
            </Badge>
          </div>
        )}

        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-800 mb-2">
            Browser Info
          </h4>
          <div className="space-y-1 text-xs text-blue-700">
            <p>
              <strong>Geolocation Support:</strong>{" "}
              {navigator.geolocation ? "Yes" : "No"}
            </p>
            <p>
              <strong>User Agent:</strong>{" "}
              {navigator.userAgent.split(" ").slice(0, 3).join(" ")}...
            </p>
            <p>
              <strong>Protocol:</strong> {window.location.protocol}
            </p>
            <p>
              <strong>Host:</strong> {window.location.host}
            </p>
            <p>
              <strong>HTTPS Required:</strong>{" "}
              {window.location.protocol === "https:"
                ? "Met"
                : "Not Met (may cause issues)"}
            </p>
          </div>
        </div>

        {/* Troubleshooting Tips */}
        {testStatus === "error" && (
          <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
            <h4 className="text-sm font-medium text-orange-800 mb-2">
              Troubleshooting Tips
            </h4>
            <div className="space-y-2 text-xs text-orange-700">
              <div>
                <p className="font-medium">For Timeout Issues:</p>
                <ul className="list-disc list-inside ml-2 space-y-1">
                  <li>
                    Move to a window or outdoor area for better GPS signal
                  </li>
                  <li>Check if location services are enabled on your device</li>
                  <li>Try a different browser (Chrome, Firefox, Safari)</li>
                  <li>Restart your browser and try again</li>
                </ul>
              </div>

              {window.location.protocol === "http:" && (
                <div>
                  <p className="font-medium text-red-700">
                    HTTPS Issue Detected:
                  </p>
                  <ul className="list-disc list-inside ml-2 space-y-1">
                    <li>Many browsers require HTTPS for geolocation</li>
                    <li>
                      Try accessing via:{" "}
                      <code className="bg-orange-100 px-1 rounded">
                        https://localhost:8080
                      </code>
                    </li>
                    <li>
                      Or use a browser that allows HTTP geolocation (Firefox)
                    </li>
                  </ul>
                </div>
              )}

              <div>
                <p className="font-medium">Alternative Solutions:</p>
                <ul className="list-disc list-inside ml-2 space-y-1">
                  <li>Use manual coordinates (see below)</li>
                  <li>Try on a mobile device with better GPS</li>
                  <li>Check browser location permissions in settings</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Manual Location Input */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-purple-800">
              Manual Location Override
            </h4>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowManualInput(!showManualInput)}
              className="text-xs h-6"
            >
              {showManualInput ? "Hide" : "Show"}
            </Button>
          </div>

          {showManualInput && (
            <div className="space-y-3">
              <p className="text-xs text-purple-700">
                Enter your coordinates manually if geolocation fails. You can
                get these from Google Maps.
              </p>

              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-xs text-purple-700 block mb-1">
                    Latitude
                  </label>
                  <Input
                    type="number"
                    step="any"
                    placeholder="40.7128"
                    value={manualLat}
                    onChange={(e) => setManualLat(e.target.value)}
                    className="text-xs h-8"
                  />
                </div>
                <div>
                  <label className="text-xs text-purple-700 block mb-1">
                    Longitude
                  </label>
                  <Input
                    type="number"
                    step="any"
                    placeholder="-74.0060"
                    value={manualLng}
                    onChange={(e) => setManualLng(e.target.value)}
                    className="text-xs h-8"
                  />
                </div>
              </div>

              <Button
                size="sm"
                onClick={setManualLocation}
                disabled={!manualLat || !manualLng}
                className="w-full text-xs h-8"
              >
                <Navigation className="h-3 w-3 mr-1" />
                Set Manual Location
              </Button>

              <div className="text-xs text-purple-600 space-y-1">
                <p>
                  <strong>How to get coordinates:</strong>
                </p>
                <ol className="list-decimal list-inside ml-2 space-y-1">
                  <li>Open Google Maps</li>
                  <li>Right-click on your location</li>
                  <li>Click the coordinates that appear</li>
                  <li>Copy and paste them here</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationTest;
