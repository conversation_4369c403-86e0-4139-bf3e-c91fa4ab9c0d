import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin, AlertCircle, CheckCircle, Clock } from 'lucide-react';

const LocationTest: React.FC = () => {
  const [testStatus, setTestStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [location, setLocation] = useState<{lat: number, lng: number} | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [accuracy, setAccuracy] = useState<number | null>(null);

  const testGeolocation = () => {
    setTestStatus('testing');
    setError(null);
    setLocation(null);
    setAccuracy(null);

    console.log('🧪 LocationTest: Starting geolocation test...');

    if (!navigator.geolocation) {
      setTestStatus('error');
      setError('Geolocation is not supported by this browser');
      console.log('❌ LocationTest: Geolocation not supported');
      return;
    }

    // Test geolocation with high accuracy
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        setLocation(coords);
        setAccuracy(position.coords.accuracy);
        setTestStatus('success');
        console.log('✅ LocationTest: Success!', coords);
        console.log('📍 LocationTest: Accuracy:', position.coords.accuracy, 'meters');
        console.log('🕐 LocationTest: Timestamp:', new Date(position.timestamp));
      },
      (error) => {
        setTestStatus('error');
        let errorMessage = '';
        
        switch(error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user. Please allow location access and try again.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable. Check your device settings.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out. Try again or check your connection.';
            break;
          default:
            errorMessage = 'An unknown error occurred while retrieving location.';
            break;
        }
        
        setError(errorMessage);
        console.log('❌ LocationTest: Error code:', error.code);
        console.log('❌ LocationTest: Error message:', error.message);
        console.log('❌ LocationTest: Detailed error:', errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0 // Force fresh location
      }
    );
  };

  const getStatusIcon = () => {
    switch(testStatus) {
      case 'testing':
        return <Clock className="h-4 w-4 text-yellow-600 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <MapPin className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = () => {
    switch(testStatus) {
      case 'testing':
        return 'border-yellow-200 bg-yellow-50';
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <Card className={`${getStatusColor()}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Location Test
        </CardTitle>
        <CardDescription>
          Test your browser's geolocation capabilities
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testGeolocation} 
          disabled={testStatus === 'testing'}
          className="w-full"
        >
          {testStatus === 'testing' ? 'Testing Location...' : 'Test My Location'}
        </Button>

        {testStatus === 'testing' && (
          <div className="p-3 bg-yellow-100 rounded-lg border border-yellow-200">
            <p className="text-sm text-yellow-800">
              Testing geolocation... This may take up to 15 seconds.
            </p>
            <p className="text-xs text-yellow-600 mt-1">
              Please allow location access when prompted by your browser.
            </p>
          </div>
        )}

        {testStatus === 'success' && location && (
          <div className="p-3 bg-green-100 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Location Detected!</span>
            </div>
            <div className="space-y-1 text-xs text-green-700">
              <p><strong>Latitude:</strong> {location.lat.toFixed(6)}</p>
              <p><strong>Longitude:</strong> {location.lng.toFixed(6)}</p>
              {accuracy && (
                <p><strong>Accuracy:</strong> ±{Math.round(accuracy)} meters</p>
              )}
            </div>
            <Badge variant="outline" className="mt-2 text-green-700 border-green-300">
              Geolocation Working
            </Badge>
          </div>
        )}

        {testStatus === 'error' && error && (
          <div className="p-3 bg-red-100 rounded-lg border border-red-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-800">Location Test Failed</span>
            </div>
            <p className="text-xs text-red-700">{error}</p>
            <Badge variant="destructive" className="mt-2">
              Geolocation Issue
            </Badge>
          </div>
        )}

        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-sm font-medium text-blue-800 mb-2">Browser Info</h4>
          <div className="space-y-1 text-xs text-blue-700">
            <p><strong>Geolocation Support:</strong> {navigator.geolocation ? 'Yes' : 'No'}</p>
            <p><strong>User Agent:</strong> {navigator.userAgent.split(' ').slice(0, 3).join(' ')}...</p>
            <p><strong>Protocol:</strong> {window.location.protocol}</p>
            <p><strong>Host:</strong> {window.location.host}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationTest;
