
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Phone, Bell, Eye, Users } from 'lucide-react';

interface EmergencyPanelProps {
  emergencyMode: boolean;
  onEmergencyToggle: (mode: boolean) => void;
}

const EmergencyPanel: React.FC<EmergencyPanelProps> = ({ emergencyMode, onEmergencyToggle }) => {
  const [countdown, setCountdown] = useState<number | null>(null);
  const [emergencyContacts] = useState([
    { name: 'Emergency Services', number: '911', type: 'emergency' },
    { name: '<PERSON> (<PERSON>)', number: '******-0123', type: 'family' },
    { name: '<PERSON> (<PERSON>)', number: '******-0456', type: 'friend' }
  ]);

  // Discreet activation methods
  const [activationMethods] = useState([
    { method: 'Triple tap power button', enabled: true },
    { method: 'Shake device vigorously', enabled: true },
    { method: 'Voice command "Help me"', enabled: false },
    { method: 'Panic word detection', enabled: true }
  ]);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown !== null && countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0) {
      // Trigger emergency alert
      handleEmergencyActivation();
      setCountdown(null);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleEmergencyActivation = () => {
    onEmergencyToggle(true);
    console.log('Emergency activated! Sending alerts...');
    // This would trigger:
    // - Location sharing with emergency contacts
    // - Automatic voice recording
    // - Photo/video capture
    // - Alert to emergency services if configured
  };

  const handleQuickActivation = () => {
    setCountdown(5); // 5-second countdown for accidental activation prevention
  };

  const handleCancelEmergency = () => {
    setCountdown(null);
    onEmergencyToggle(false);
  };

  const handleDiscreetAlert = () => {
    // Send silent alert to trusted contacts
    console.log('Discreet alert sent to trusted contacts');
  };

  return (
    <Card className={emergencyMode ? 'border-red-500 bg-red-50' : ''}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-red-600">
          <Shield className="h-5 w-5" />
          Emergency Features
        </CardTitle>
        <CardDescription>
          Quick access to emergency services and safety features
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Emergency Status */}
        {emergencyMode && (
          <Alert className="border-red-500 bg-red-50">
            <Shield className="h-4 w-4" />
            <AlertDescription className="text-red-700">
              Emergency mode is ACTIVE. Your location is being shared with trusted contacts.
            </AlertDescription>
          </Alert>
        )}

        {/* Countdown Alert */}
        {countdown !== null && (
          <Alert className="border-orange-500 bg-orange-50">
            <Bell className="h-4 w-4" />
            <AlertDescription className="text-orange-700">
              Emergency alert in {countdown} seconds. Tap cancel to stop.
            </AlertDescription>
          </Alert>
        )}

        {/* Emergency Buttons */}
        <div className="grid grid-cols-1 gap-3">
          {!emergencyMode && countdown === null && (
            <>
              <Button
                variant="destructive"
                size="lg"
                onClick={handleQuickActivation}
                className="h-12 text-lg font-semibold"
              >
                <Phone className="h-5 w-5 mr-2" />
                Emergency Alert
              </Button>
              
              <Button
                variant="outline"
                onClick={handleDiscreetAlert}
                className="border-orange-500 text-orange-600 hover:bg-orange-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                Discreet Alert
              </Button>
            </>
          )}

          {countdown !== null && (
            <Button
              variant="outline"
              size="lg"
              onClick={handleCancelEmergency}
              className="h-12 text-lg font-semibold border-gray-400"
            >
              Cancel Emergency
            </Button>
          )}

          {emergencyMode && (
            <Button
              variant="outline"
              size="lg"
              onClick={handleCancelEmergency}
              className="h-12 text-lg font-semibold border-green-500 text-green-600 hover:bg-green-50"
            >
              I'm Safe - Deactivate
            </Button>
          )}
        </div>

        {/* Emergency Contacts */}
        <div>
          <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Emergency Contacts
          </h4>
          <div className="space-y-2">
            {emergencyContacts.map((contact, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <div>
                  <p className="text-sm font-medium">{contact.name}</p>
                  <p className="text-xs text-gray-500">{contact.number}</p>
                </div>
                <Badge variant={contact.type === 'emergency' ? 'destructive' : 'secondary'}>
                  {contact.type}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* Discreet Activation Methods */}
        <div>
          <h4 className="font-medium text-sm mb-2">Discreet Activation</h4>
          <div className="space-y-2">
            {activationMethods.map((method, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <span className="text-gray-700">{method.method}</span>
                <Badge variant={method.enabled ? 'default' : 'secondary'}>
                  {method.enabled ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
            ))}
          </div>
        </div>

        {/* AI Safety Advice */}
        <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="font-medium text-sm text-blue-800 mb-1">AI Safety Tip</h4>
          <p className="text-xs text-blue-700">
            Based on your location and time, consider staying in well-lit areas and keeping emergency contacts informed of your journey.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmergencyPanel;
