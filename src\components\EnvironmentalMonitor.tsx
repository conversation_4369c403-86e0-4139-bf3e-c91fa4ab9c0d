
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Eye, Users, Shield, Bell } from 'lucide-react';

const EnvironmentalMonitor = () => {
  const [environmentalData, setEnvironmentalData] = useState({
    lighting: {
      level: 78,
      quality: 'Good',
      forecast: 'Decreasing after sunset'
    },
    crowdDensity: {
      level: 65,
      quality: 'Moderate',
      trend: 'Increasing'
    },
    noiseLevel: {
      level: 45,
      quality: 'Quiet',
      impact: 'Low'
    },
    visibilityScore: {
      level: 82,
      factors: ['Clear weather', 'Good lighting', 'Open area']
    }
  });

  const [aiPredictions, setAiPredictions] = useState({
    nextHourSafety: 85,
    peakDangerTime: '11:30 PM - 1:00 AM',
    recommendedActions: [
      'Avoid dimly lit areas after 9 PM',
      'Stay in moderate to high crowd density zones',
      'Consider rideshare for late evening travel'
    ]
  });

  useEffect(() => {
    // Simulate real-time environmental monitoring
    const interval = setInterval(() => {
      setEnvironmentalData(prev => ({
        ...prev,
        lighting: {
          ...prev.lighting,
          level: Math.max(20, prev.lighting.level - (Math.random() * 2)) // Simulate decreasing light
        },
        crowdDensity: {
          ...prev.crowdDensity,
          level: Math.max(10, Math.min(90, prev.crowdDensity.level + (Math.random() * 10 - 5)))
        },
        noiseLevel: {
          ...prev.noiseLevel,
          level: Math.max(20, Math.min(80, prev.noiseLevel.level + (Math.random() * 6 - 3)))
        }
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getQualityColor = (level: number) => {
    if (level >= 70) return 'text-green-600';
    if (level >= 40) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressColor = (level: number) => {
    if (level >= 70) return 'bg-green-500';
    if (level >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatProgress = (level: number) => {
    return Math.round(level);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Environmental Monitor
        </CardTitle>
        <CardDescription>
          Real-time environmental factors affecting your safety
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Environmental Metrics */}
        <div className="space-y-4">
          {/* Lighting Conditions */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Lighting Quality</span>
              </div>
              <Badge variant="outline" className={getQualityColor(environmentalData.lighting.level)}>
                {environmentalData.lighting.quality}
              </Badge>
            </div>
            <Progress 
              value={formatProgress(environmentalData.lighting.level)} 
              className="h-2"
            />
            <p className="text-xs text-gray-500 mt-1">{environmentalData.lighting.forecast}</p>
          </div>

          {/* Crowd Density */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Crowd Density</span>
              </div>
              <Badge variant="outline" className={getQualityColor(environmentalData.crowdDensity.level)}>
                {environmentalData.crowdDensity.quality}
              </Badge>
            </div>
            <Progress 
              value={formatProgress(environmentalData.crowdDensity.level)} 
              className="h-2"
            />
            <p className="text-xs text-gray-500 mt-1">Trend: {environmentalData.crowdDensity.trend}</p>
          </div>

          {/* Noise Level */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Ambient Noise</span>
              </div>
              <Badge variant="outline" className={getQualityColor(100 - environmentalData.noiseLevel.level)}>
                {environmentalData.noiseLevel.quality}
              </Badge>
            </div>
            <Progress 
              value={formatProgress(environmentalData.noiseLevel.level)} 
              className="h-2"
            />
            <p className="text-xs text-gray-500 mt-1">Impact: {environmentalData.noiseLevel.impact}</p>
          </div>

          {/* Overall Visibility Score */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Visibility Score</span>
              </div>
              <Badge variant="outline" className={getQualityColor(environmentalData.visibilityScore.level)}>
                {formatProgress(environmentalData.visibilityScore.level)}%
              </Badge>
            </div>
            <Progress 
              value={formatProgress(environmentalData.visibilityScore.level)} 
              className="h-2"
            />
            <div className="text-xs text-gray-500 mt-1">
              <p>Contributing factors:</p>
              <ul className="list-disc list-inside ml-2">
                {environmentalData.visibilityScore.factors.map((factor, index) => (
                  <li key={index}>{factor}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* AI Predictions */}
        <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <Shield className="h-4 w-4 text-purple-600" />
            AI Safety Predictions
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">Next Hour Safety Score</span>
              <Badge className={getQualityColor(aiPredictions.nextHourSafety)}>
                {aiPredictions.nextHourSafety}%
              </Badge>
            </div>
            
            <div>
              <p className="text-sm text-gray-700 mb-1">Peak Risk Period</p>
              <p className="text-xs text-red-600 font-medium">{aiPredictions.peakDangerTime}</p>
            </div>
            
            <div>
              <p className="text-sm text-gray-700 mb-2">AI Recommendations</p>
              <ul className="space-y-1">
                {aiPredictions.recommendedActions.map((action, index) => (
                  <li key={index} className="text-xs text-gray-600 flex items-start gap-2">
                    <span className="text-blue-500">•</span>
                    {action}
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnvironmentalMonitor;
