# Manual Location Setup Guide

## Issue: Location Request Timed Out

Since the automatic geolocation is timing out, you can now use the **Manual Location Override** feature to set your location manually.

## Quick Solution Steps

### Step 1: Get Your Coordinates
1. **Open Google Maps** in a new tab
2. **Find your location** on the map
3. **Right-click** on your exact location
4. **Click the coordinates** that appear in the popup (they look like: `40.7128, -74.0060`)
5. The coordinates will be **copied to your clipboard**

### Step 2: Set Manual Location
1. In the application, look for the **"Location Test"** card (purple section)
2. Click **"Show"** next to "Manual Location Override"
3. **Paste your coordinates**:
   - **Latitude** in the first box (e.g., `40.7128`)
   - **Longitude** in the second box (e.g., `-74.0060`)
4. Click **"Set Manual Location"**

### Step 3: Verify It Works
1. The Location Test should show **"Location Detected!"** with your coordinates
2. The **Location Status** card should turn **green** and show "Active"
3. Your coordinates should appear under the status
4. The **SafetyMap** should center on your location when you add a Mapbox token

## Example Coordinates for Testing

If you want to test with some known locations:

### Major Cities
- **New York City**: `40.7128, -74.0060`
- **Los Angeles**: `34.0522, -118.2437`
- **Chicago**: `41.8781, -87.6298`
- **London**: `51.5074, -0.1278`
- **Paris**: `48.8566, 2.3522`
- **Tokyo**: `35.6762, 139.6503`

### How to Use Example Coordinates
1. Copy any latitude/longitude pair above
2. Paste the first number (latitude) in the Latitude field
3. Paste the second number (longitude) in the Longitude field
4. Click "Set Manual Location"

## Why This Happens

The location timeout can occur due to:

1. **HTTPS Requirement**: Many browsers require HTTPS for geolocation
   - The app is running on HTTP (`http://localhost:8080`)
   - Try accessing via `https://localhost:8080` (may not work without SSL certificate)

2. **Browser Security**: Some browsers block geolocation on localhost
   - Chrome/Safari are stricter about this
   - Firefox is more permissive

3. **Device Settings**: Location services might be disabled
   - Check your system's location/privacy settings

4. **Poor GPS Signal**: Indoor locations or areas with poor GPS reception
   - Try near a window or outdoors

## Troubleshooting the Manual Input

### Invalid Coordinates Error
- **Latitude** must be between -90 and 90
- **Longitude** must be between -180 and 180
- Use decimal format (e.g., `40.7128`, not `40°42'46"N`)

### Getting More Precise Coordinates
1. In Google Maps, **zoom in** as much as possible to your exact location
2. Right-click on the **precise spot** you want
3. The coordinates will be more accurate

### Alternative Coordinate Sources
- **Apple Maps**: Right-click → "Copy Coordinates"
- **GPS apps** on your phone
- **What3Words**: Convert to coordinates using their website
- **GPS coordinate websites**: Search for your address

## Expected Results

Once you set manual coordinates:

1. ✅ **Location Status**: Green dot, "Active"
2. ✅ **Coordinates Display**: Your exact lat/lng shown
3. ✅ **SafetyMap**: Centers on your location (with Mapbox token)
4. ✅ **RouteOptimizer**: Uses your location as starting point
5. ✅ **Console Logs**: Shows "Manual location received" messages

## Permanent Fix Options

For a permanent solution to the geolocation timeout:

### Option 1: Use HTTPS
- Set up SSL certificate for localhost
- Access via `https://localhost:8080`

### Option 2: Use Different Browser
- Try **Firefox** (more permissive with HTTP geolocation)
- Try **Safari** or **Edge**

### Option 3: Deploy to HTTPS Server
- Deploy the app to a server with HTTPS
- Use services like Vercel, Netlify, or GitHub Pages

### Option 4: Mobile Testing
- Test on a mobile device
- Mobile browsers often have better GPS access

## Testing the Fix

After setting manual coordinates:

1. **Check Location Status**: Should show green "Active"
2. **Test SafetyMap**: Enter Mapbox token, verify blue marker appears at your location
3. **Test RouteOptimizer**: Enter a destination, verify routes generate from your location
4. **Check Console**: Look for success messages in browser console

The manual location override provides a reliable workaround for geolocation issues while maintaining full functionality of the safety mapping features.
