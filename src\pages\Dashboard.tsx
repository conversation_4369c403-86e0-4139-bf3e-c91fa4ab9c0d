import React, { useState, useEffect } from "react";
import { Shield, MapPin, Bell, Users, Eye } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import SafetyMap from "@/components/SafetyMap";
import RouteOptimizer from "@/components/RouteOptimizer";
import EmergencyPanel from "@/components/EmergencyPanel";
import SafetyAlerts from "@/components/SafetyAlerts";
import EnvironmentalMonitor from "@/components/EnvironmentalMonitor";
import LocationTest from "@/components/LocationTest";

const Dashboard = () => {
  const [currentLocation, setCurrentLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const [locationStatus, setLocationStatus] = useState<
    "requesting" | "granted" | "denied" | "unavailable"
  >("requesting");
  const [safetyScore, setSafetyScore] = useState(85);
  const [activeAlerts, setActiveAlerts] = useState(2);
  const [emergencyMode, setEmergencyMode] = useState(false);

  useEffect(() => {
    // Get user's current location
    console.log("🌍 Dashboard: Starting geolocation request...");

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(newLocation);
          setLocationStatus("granted");
          console.log("✅ Dashboard: Current location detected:", newLocation);
          console.log("📍 Accuracy:", position.coords.accuracy, "meters");
        },
        (error) => {
          console.error("❌ Dashboard: Error getting location:", error);
          console.error("Error code:", error.code);
          console.error("Error message:", error.message);

          // More specific error handling
          let errorMessage = "";
          let status: "denied" | "unavailable" = "unavailable";

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Location access denied by user";
              status = "denied";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Location information unavailable";
              status = "unavailable";
              break;
            case error.TIMEOUT:
              errorMessage = "Location request timed out";
              status = "unavailable";
              break;
            default:
              errorMessage = "Unknown location error";
              status = "unavailable";
              break;
          }
          console.log("🔍 Error details:", errorMessage);
          setLocationStatus(status);

          // Fallback to NYC coordinates for demo purposes
          const fallbackLocation = { lat: 40.7128, lng: -74.006 };
          setCurrentLocation(fallbackLocation);
          console.log(
            "🏙️ Dashboard: Using fallback location:",
            fallbackLocation
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 15000, // Increased timeout
          maximumAge: 60000, // Reduced cache time to 1 minute
        }
      );
    } else {
      console.log(
        "❌ Dashboard: Geolocation not supported, using fallback location"
      );
      setLocationStatus("unavailable");
      setCurrentLocation({ lat: 40.7128, lng: -74.006 });
    }
  }, []);

  // Listen for manual location events from LocationTest component
  useEffect(() => {
    const handleManualLocation = (event: CustomEvent) => {
      const manualCoords = event.detail;
      console.log("🎯 Dashboard: Manual location received:", manualCoords);
      setCurrentLocation(manualCoords);
      setLocationStatus("granted");
    };

    window.addEventListener(
      "manualLocationSet",
      handleManualLocation as EventListener
    );

    return () => {
      window.removeEventListener(
        "manualLocationSet",
        handleManualLocation as EventListener
      );
    };
  }, []);

  const getSafetyScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getSafetyScoreText = (score: number) => {
    if (score >= 80) return "High Safety";
    if (score >= 60) return "Moderate Safety";
    return "Low Safety";
  };

  const requestLocationAgain = () => {
    console.log("🔄 Dashboard: Manual location refresh requested");
    setLocationStatus("requesting");

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(newLocation);
          setLocationStatus("granted");
          console.log(
            "✅ Dashboard: Manual location refresh successful:",
            newLocation
          );
        },
        (error) => {
          console.error("❌ Dashboard: Manual location refresh failed:", error);
          setLocationStatus(
            error.code === error.PERMISSION_DENIED ? "denied" : "unavailable"
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 0, // Force fresh location
        }
      );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Sentinel Safe Paths
          </h1>
          <p className="text-gray-600">AI-Powered Personal Safety Companion</p>
        </div>

        {/* Safety Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Safety Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${getSafetyScoreColor(
                    safetyScore
                  )}`}
                ></div>
                <span className="text-2xl font-bold">{safetyScore}%</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {getSafetyScoreText(safetyScore)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Location Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    locationStatus === "granted"
                      ? "bg-green-500"
                      : locationStatus === "requesting"
                      ? "bg-yellow-500"
                      : locationStatus === "denied"
                      ? "bg-red-500"
                      : "bg-gray-500"
                  }`}
                ></div>
                <span className="text-sm font-semibold">
                  {locationStatus === "granted"
                    ? "Active"
                    : locationStatus === "requesting"
                    ? "Requesting..."
                    : locationStatus === "denied"
                    ? "Denied"
                    : "Unavailable"}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {locationStatus === "granted"
                  ? "GPS tracking enabled"
                  : locationStatus === "requesting"
                  ? "Waiting for location access"
                  : locationStatus === "denied"
                  ? "Location access denied"
                  : "GPS not available"}
              </p>
              {currentLocation && (
                <p className="text-xs text-blue-600 mt-1">
                  {currentLocation.lat.toFixed(4)},{" "}
                  {currentLocation.lng.toFixed(4)}
                </p>
              )}
              {(locationStatus === "denied" ||
                locationStatus === "unavailable") && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={requestLocationAgain}
                  className="mt-2 text-xs h-6"
                  disabled={locationStatus === "requesting"}
                >
                  {locationStatus === "requesting"
                    ? "Requesting..."
                    : "Retry Location"}
                </Button>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Active Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Badge variant={activeAlerts > 0 ? "destructive" : "secondary"}>
                  {activeAlerts}
                </Badge>
              </div>
              <p className="text-xs text-gray-500 mt-1">Safety notifications</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Community
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-semibold">247</span>
                <span className="text-xs text-gray-500">nearby users</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Connected safely</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Map and Route */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Safety Map & Route Planning
                </CardTitle>
                <CardDescription>
                  Real-time safety visualization and optimized route suggestions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SafetyMap currentLocation={currentLocation} />
              </CardContent>
            </Card>

            <RouteOptimizer />
          </div>

          {/* Right Column - Panels */}
          <div className="space-y-6">
            <LocationTest />

            <EmergencyPanel
              emergencyMode={emergencyMode}
              onEmergencyToggle={setEmergencyMode}
            />

            <SafetyAlerts alerts={activeAlerts} />

            <EnvironmentalMonitor />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
