import React, { useState, useEffect } from "react";
import { Shield, MapPin, Bell, Users, Eye } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import SafetyMap from "@/components/SafetyMap";
import RouteOptimizer from "@/components/RouteOptimizer";
import EmergencyPanel from "@/components/EmergencyPanel";
import SafetyAlerts from "@/components/SafetyAlerts";
import EnvironmentalMonitor from "@/components/EnvironmentalMonitor";

const Dashboard = () => {
  const [currentLocation, setCurrentLocation] = useState<{
    lat: number;
    lng: number;
  } | null>(null);
  const [safetyScore, setSafetyScore] = useState(85);
  const [activeAlerts, setActiveAlerts] = useState(2);
  const [emergencyMode, setEmergencyMode] = useState(false);

  useEffect(() => {
    // Get user's current location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(newLocation);
          console.log("Current location detected:", newLocation);
        },
        (error) => {
          console.error("Error getting location:", error);
          // Fallback to NYC coordinates for demo purposes
          const fallbackLocation = { lat: 40.7128, lng: -74.006 };
          setCurrentLocation(fallbackLocation);
          console.log("Using fallback location:", fallbackLocation);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000, // 5 minutes
        }
      );
    } else {
      console.log("Geolocation not supported, using fallback location");
      setCurrentLocation({ lat: 40.7128, lng: -74.006 });
    }
  }, []);

  const getSafetyScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getSafetyScoreText = (score: number) => {
    if (score >= 80) return "High Safety";
    if (score >= 60) return "Moderate Safety";
    return "Low Safety";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center justify-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Sentinel Safe Paths
          </h1>
          <p className="text-gray-600">AI-Powered Personal Safety Companion</p>
        </div>

        {/* Safety Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Safety Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${getSafetyScoreColor(
                    safetyScore
                  )}`}
                ></div>
                <span className="text-2xl font-bold">{safetyScore}%</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {getSafetyScoreText(safetyScore)}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Location Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
                <span className="text-sm font-semibold">Active</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">GPS tracking enabled</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Active Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Badge variant={activeAlerts > 0 ? "destructive" : "secondary"}>
                  {activeAlerts}
                </Badge>
              </div>
              <p className="text-xs text-gray-500 mt-1">Safety notifications</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Community
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-semibold">247</span>
                <span className="text-xs text-gray-500">nearby users</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Connected safely</p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Map and Route */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Safety Map & Route Planning
                </CardTitle>
                <CardDescription>
                  Real-time safety visualization and optimized route suggestions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <SafetyMap currentLocation={currentLocation} />
              </CardContent>
            </Card>

            <RouteOptimizer />
          </div>

          {/* Right Column - Panels */}
          <div className="space-y-6">
            <EmergencyPanel
              emergencyMode={emergencyMode}
              onEmergencyToggle={setEmergencyMode}
            />

            <SafetyAlerts alerts={activeAlerts} />

            <EnvironmentalMonitor />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
