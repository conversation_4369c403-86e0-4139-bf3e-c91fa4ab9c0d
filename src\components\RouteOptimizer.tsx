import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { MapPin, Shield, Eye, Users, Navigation, Route } from 'lucide-react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

interface RouteOptimizerProps {
  onRouteSelected?: (routeId: number) => void;
}

const RoutePreviewMap: React.FC<{ route: any; mapboxToken?: string }> = ({ route, mapboxToken }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    if (!mapContainer.current || !mapboxToken) return;

    try {
      mapboxgl.accessToken = mapboxToken;
      
      // Ensure coordinates are properly formatted as [lng, lat]
      const startCoordinates = route.coordinates[0] as [number, number];
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/streets-v12',
        center: startCoordinates,
        zoom: 13,
        interactive: false
      });

      map.current.on('load', () => {
        if (!map.current) return;

        // Add route line
        map.current.addSource('route', {
          'type': 'geojson',
          'data': {
            'type': 'Feature',
            'properties': {},
            'geometry': {
              'type': 'LineString',
              'coordinates': route.coordinates
            }
          }
        });

        map.current.addLayer({
          'id': 'route',
          'type': 'line',
          'source': 'route',
          'layout': {
            'line-join': 'round',
            'line-cap': 'round'
          },
          'paint': {
            'line-color': route.safetyScore >= 80 ? '#10b981' : route.safetyScore >= 60 ? '#f59e0b' : '#ef4444',
            'line-width': 4
          }
        });

        // Add start marker
        new mapboxgl.Marker({ color: '#3b82f6' })
          .setLngLat(startCoordinates)
          .addTo(map.current);

        // Add end marker
        const endCoordinates = route.coordinates[route.coordinates.length - 1] as [number, number];
        new mapboxgl.Marker({ color: '#ef4444' })
          .setLngLat(endCoordinates)
          .addTo(map.current);

        // Fit bounds to show entire route
        const bounds = new mapboxgl.LngLatBounds();
        route.coordinates.forEach((coord: [number, number]) => bounds.extend(coord));
        map.current.fitBounds(bounds, { padding: 20 });
      });

    } catch (error) {
      console.error('Error initializing route preview map:', error);
    }

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [route, mapboxToken]);

  if (!mapboxToken) {
    return (
      <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-sm text-gray-500">Map preview requires token</p>
      </div>
    );
  }

  return <div ref={mapContainer} className="h-32 rounded border" />;
};

const RouteOptimizer: React.FC<RouteOptimizerProps> = ({ onRouteSelected }) => {
  const [destination, setDestination] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [mapboxToken, setMapboxToken] = useState('');
  const [showTokenInput, setShowTokenInput] = useState(true);
  const [routes, setRoutes] = useState([
    {
      id: 1,
      name: 'Safest Route',
      duration: '18 min',
      distance: '2.1 mi',
      safetyScore: 92,
      lighting: 'excellent',
      crowdDensity: 'high',
      crimeRisk: 'low',
      features: ['Well-lit streets', 'High foot traffic', 'Security cameras'],
      coordinates: [
        [-74.0060, 40.7128], // Start point (Financial District)
        [-74.0050, 40.7135], // Move east slightly 
        [-74.0045, 40.7142], // Continue northeast
        [-74.0040, 40.7148], // Well-lit Broadway area
        [-74.0035, 40.7155], // High foot traffic zone
        [-74.0030, 40.7162], // Security camera area
        [-74.0025, 40.7168], // Continue through safe area
        [-74.0020, 40.7175], // Approach destination
        [-74.0015, 40.7182], // Final safe approach
        [-74.0010, 40.7190]  // End point (Tribeca)
      ]
    },
    {
      id: 2,
      name: 'Fastest Route',
      duration: '12 min',
      distance: '1.8 mi',
      safetyScore: 67,
      lighting: 'moderate',
      crowdDensity: 'low',
      crimeRisk: 'medium',
      features: ['Direct path', 'Some dark areas', 'Limited visibility'],
      coordinates: [
        [-74.0060, 40.7128], // Start point (Financial District)
        [-74.0045, 40.7140], // Direct northwest 
        [-74.0030, 40.7155], // Shortcut through less populated area
        [-74.0020, 40.7170], // Continue direct path
        [-74.0010, 40.7190]  // End point (Tribeca) - most direct
      ]
    },
    {
      id: 3,
      name: 'Balanced Route',
      duration: '15 min',
      distance: '2.0 mi',
      safetyScore: 78,
      lighting: 'good',
      crowdDensity: 'moderate',
      crimeRisk: 'low',
      features: ['Good lighting', 'Moderate traffic', 'Emergency services nearby'],
      coordinates: [
        [-74.0060, 40.7128], // Start point (Financial District)
        [-74.0055, 40.7135], // Slight westward curve
        [-74.0048, 40.7145], // Balanced path through moderate area
        [-74.0040, 40.7158], // Near emergency services
        [-74.0032, 40.7170], // Moderate traffic zone
        [-74.0025, 40.7180], // Continue balanced approach
        [-74.0018, 40.7188], // Final approach
        [-74.0010, 40.7190]  // End point (Tribeca)
      ]
    }
  ]);

  const [selectedRoute, setSelectedRoute] = useState<number | null>(null);

  const getSafetyColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const handleRouteSearch = async () => {
    if (!destination.trim()) return;
    
    setIsAnalyzing(true);
    console.log('Analyzing routes to:', destination);
    
    // Simulate AI route analysis
    setTimeout(() => {
      // Update routes with new analysis
      setRoutes(prev => prev.map(route => ({
        ...route,
        safetyScore: Math.floor(Math.random() * 30) + 65, // Random score between 65-95
        duration: `${Math.floor(Math.random() * 10) + 12} min`,
        distance: `${(Math.random() * 0.5 + 1.5).toFixed(1)} mi`
      })));
      setIsAnalyzing(false);
    }, 2000);
  };

  const handleRouteSelect = (routeId: number) => {
    setSelectedRoute(routeId);
    console.log('Selected route:', routeId);
    if (onRouteSelected) {
      onRouteSelected(routeId);
    }
  };

  const handleStartNavigation = () => {
    if (selectedRoute) {
      console.log('Starting navigation with route:', selectedRoute);
      // This would integrate with the map to show the route
    }
  };

  const initializeMapbox = () => {
    if (mapboxToken.trim()) {
      setShowTokenInput(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Route className="h-5 w-5" />
          AI Route Optimizer
        </CardTitle>
        <CardDescription>
          Get the safest route recommendations based on real-time data analysis
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mapbox Token Input */}
        {showTokenInput && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Enter Mapbox public token for route previews..."
                value={mapboxToken}
                onChange={(e) => setMapboxToken(e.target.value)}
                className="flex-1"
              />
              <Button onClick={initializeMapbox} disabled={!mapboxToken.trim()}>
                Enable Previews
              </Button>
            </div>
            <p className="text-xs text-blue-600">
              Get your free token at{' '}
              <a href="https://mapbox.com/" target="_blank" rel="noopener noreferrer" className="underline">
                mapbox.com
              </a>
            </p>
          </div>
        )}

        {/* Destination Input */}
        <div className="flex gap-2">
          <Input
            placeholder="Enter destination address..."
            value={destination}
            onChange={(e) => setDestination(e.target.value)}
            className="flex-1"
            onKeyPress={(e) => e.key === 'Enter' && handleRouteSearch()}
          />
          <Button 
            onClick={handleRouteSearch} 
            className="px-6"
            disabled={isAnalyzing || !destination.trim()}
          >
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Analyzing...
              </>
            ) : (
              'Analyze Routes'
            )}
          </Button>
        </div>

        {/* AI Analysis Status */}
        {isAnalyzing && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-blue-700">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm font-medium">AI analyzing safety factors...</span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Processing crime data, lighting conditions, and crowd density
            </p>
          </div>
        )}

        {/* Route Options */}
        <div className="space-y-3">
          {routes.map((route) => (
            <div
              key={route.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRoute === route.id
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }`}
              onClick={() => handleRouteSelect(route.id)}
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    {route.name}
                    {selectedRoute === route.id && (
                      <Navigation className="h-4 w-4 text-blue-600" />
                    )}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span>{route.duration}</span>
                    <span>•</span>
                    <span>{route.distance}</span>
                  </div>
                </div>
                <Badge className={getSafetyColor(route.safetyScore)}>
                  {route.safetyScore}% Safe
                </Badge>
              </div>

              {/* Route Preview Map */}
              <div className="mb-3">
                <RoutePreviewMap route={route} mapboxToken={mapboxToken} />
              </div>

              {/* Environmental Factors */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="text-center">
                  <Eye className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Lighting</p>
                  <p className="text-sm font-medium capitalize">{route.lighting}</p>
                </div>
                <div className="text-center">
                  <Users className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Crowd Density</p>
                  <p className="text-sm font-medium capitalize">{route.crowdDensity}</p>
                </div>
                <div className="text-center">
                  <Shield className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className={`text-sm font-medium capitalize ${getRiskColor(route.crimeRisk)}`}>
                    {route.crimeRisk}
                  </p>
                </div>
              </div>

              {/* Route Features */}
              <div className="space-y-1">
                <p className="text-xs text-gray-500 font-medium">Key Features:</p>
                <div className="flex flex-wrap gap-1">
                  {route.features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Real-time Data Indicators */}
              {selectedRoute === route.id && (
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Live traffic: Good</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Weather: Clear</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {selectedRoute && (
          <Button className="w-full" size="lg" onClick={handleStartNavigation}>
            Start Navigation with Route {selectedRoute}
          </Button>
        )}

        {/* Real-time Safety Tips */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-medium text-sm text-purple-800 mb-2">AI Safety Tips</h4>
          <div className="space-y-1 text-xs text-purple-700">
            <p>• Share your route with trusted contacts</p>
            <p>• Stay alert and avoid distractions</p>
            <p>• Trust your instincts - change route if needed</p>
            <p>• Keep emergency contacts easily accessible</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouteOptimizer;
