import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Shield,
  Eye,
  Users,
  Navigation,
  Route,
  AlertCircle,
} from "lucide-react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

interface RouteOptimizerProps {
  onRouteSelected?: (routeId: number) => void;
}

interface Coordinates {
  lat: number;
  lng: number;
}

interface RouteData {
  id: number;
  name: string;
  duration: string;
  distance: string;
  safetyScore: number;
  lighting: string;
  crowdDensity: string;
  crimeRisk: string;
  features: string[];
  coordinates: [number, number][];
}

// Geocoding utility functions
const geocodeAddress = async (
  address: string,
  mapboxToken: string
): Promise<Coordinates | null> => {
  if (!mapboxToken || !address.trim()) return null;

  try {
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
        address
      )}.json?access_token=${mapboxToken}&limit=1`
    );

    if (!response.ok) throw new Error("Geocoding failed");

    const data = await response.json();

    if (data.features && data.features.length > 0) {
      const [lng, lat] = data.features[0].center;
      return { lat, lng };
    }

    return null;
  } catch (error) {
    console.error("Geocoding error:", error);
    return null;
  }
};

// Generate route coordinates between two points
const generateRouteCoordinates = (
  start: Coordinates,
  end: Coordinates,
  routeType: "safest" | "fastest" | "balanced"
): [number, number][] => {
  const startCoord: [number, number] = [start.lng, start.lat];
  const endCoord: [number, number] = [end.lng, end.lat];

  // Calculate the difference
  const deltaLng = end.lng - start.lng;
  const deltaLat = end.lat - start.lat;

  // Generate intermediate points based on route type
  const points: [number, number][] = [startCoord];

  const numPoints =
    routeType === "fastest" ? 3 : routeType === "safest" ? 8 : 5;

  for (let i = 1; i < numPoints; i++) {
    const progress = i / numPoints;

    // Add some variation based on route type
    let lngOffset = 0;
    let latOffset = 0;

    if (routeType === "safest") {
      // Safest route: more winding, avoids direct paths
      lngOffset = Math.sin(progress * Math.PI * 2) * Math.abs(deltaLng) * 0.1;
      latOffset =
        Math.cos(progress * Math.PI * 1.5) * Math.abs(deltaLat) * 0.05;
    } else if (routeType === "balanced") {
      // Balanced route: slight curves
      lngOffset = Math.sin(progress * Math.PI) * Math.abs(deltaLng) * 0.05;
      latOffset = Math.cos(progress * Math.PI) * Math.abs(deltaLat) * 0.03;
    }
    // Fastest route: minimal offset (almost direct)

    const lng = start.lng + deltaLng * progress + lngOffset;
    const lat = start.lat + deltaLat * progress + latOffset;

    points.push([lng, lat]);
  }

  points.push(endCoord);
  return points;
};

// Calculate distance between two coordinates (rough estimate)
const calculateDistance = (start: Coordinates, end: Coordinates): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = ((end.lat - start.lat) * Math.PI) / 180;
  const dLng = ((end.lng - start.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((start.lat * Math.PI) / 180) *
      Math.cos((end.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const RoutePreviewMap: React.FC<{ route: RouteData; mapboxToken?: string }> = ({
  route,
  mapboxToken,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    if (!mapContainer.current || !mapboxToken) return;

    try {
      mapboxgl.accessToken = mapboxToken;

      // Ensure coordinates are properly formatted as [lng, lat]
      const startCoordinates = route.coordinates[0] as [number, number];

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: startCoordinates,
        zoom: 13,
        interactive: false,
      });

      map.current.on("load", () => {
        if (!map.current) return;

        // Add route line
        map.current.addSource("route", {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {},
            geometry: {
              type: "LineString",
              coordinates: route.coordinates,
            },
          },
        });

        map.current.addLayer({
          id: "route",
          type: "line",
          source: "route",
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color":
              route.safetyScore >= 80
                ? "#10b981"
                : route.safetyScore >= 60
                ? "#f59e0b"
                : "#ef4444",
            "line-width": 4,
          },
        });

        // Add start marker
        new mapboxgl.Marker({ color: "#3b82f6" })
          .setLngLat(startCoordinates)
          .addTo(map.current);

        // Add end marker
        const endCoordinates = route.coordinates[
          route.coordinates.length - 1
        ] as [number, number];
        new mapboxgl.Marker({ color: "#ef4444" })
          .setLngLat(endCoordinates)
          .addTo(map.current);

        // Fit bounds to show entire route
        const bounds = new mapboxgl.LngLatBounds();
        route.coordinates.forEach((coord: [number, number]) =>
          bounds.extend(coord)
        );
        map.current.fitBounds(bounds, { padding: 20 });
      });
    } catch (error) {
      console.error("Error initializing route preview map:", error);
    }

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [route, mapboxToken]);

  if (!mapboxToken) {
    return (
      <div className="h-32 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-sm text-gray-500">Map preview requires token</p>
      </div>
    );
  }

  return <div ref={mapContainer} className="h-32 rounded border" />;
};

const RouteOptimizer: React.FC<RouteOptimizerProps> = ({ onRouteSelected }) => {
  const [destination, setDestination] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [mapboxToken, setMapboxToken] = useState("");
  const [showTokenInput, setShowTokenInput] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(
    null
  );
  const [destinationCoords, setDestinationCoords] =
    useState<Coordinates | null>(null);
  const [geocodingError, setGeocodingError] = useState<string | null>(null);
  const [routes, setRoutes] = useState<RouteData[]>([
    {
      id: 1,
      name: "Safest Route",
      duration: "18 min",
      distance: "2.1 mi",
      safetyScore: 92,
      lighting: "excellent",
      crowdDensity: "high",
      crimeRisk: "low",
      features: ["Well-lit streets", "High foot traffic", "Security cameras"],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.005, 40.7135], // Move east slightly
        [-74.0045, 40.7142], // Continue northeast
        [-74.004, 40.7148], // Well-lit Broadway area
        [-74.0035, 40.7155], // High foot traffic zone
        [-74.003, 40.7162], // Security camera area
        [-74.0025, 40.7168], // Continue through safe area
        [-74.002, 40.7175], // Approach destination
        [-74.0015, 40.7182], // Final safe approach
        [-74.001, 40.719], // End point (Tribeca)
      ],
    },
    {
      id: 2,
      name: "Fastest Route",
      duration: "12 min",
      distance: "1.8 mi",
      safetyScore: 67,
      lighting: "moderate",
      crowdDensity: "low",
      crimeRisk: "medium",
      features: ["Direct path", "Some dark areas", "Limited visibility"],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.0045, 40.714], // Direct northwest
        [-74.003, 40.7155], // Shortcut through less populated area
        [-74.002, 40.717], // Continue direct path
        [-74.001, 40.719], // End point (Tribeca) - most direct
      ],
    },
    {
      id: 3,
      name: "Balanced Route",
      duration: "15 min",
      distance: "2.0 mi",
      safetyScore: 78,
      lighting: "good",
      crowdDensity: "moderate",
      crimeRisk: "low",
      features: [
        "Good lighting",
        "Moderate traffic",
        "Emergency services nearby",
      ],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.0055, 40.7135], // Slight westward curve
        [-74.0048, 40.7145], // Balanced path through moderate area
        [-74.004, 40.7158], // Near emergency services
        [-74.0032, 40.717], // Moderate traffic zone
        [-74.0025, 40.718], // Continue balanced approach
        [-74.0018, 40.7188], // Final approach
        [-74.001, 40.719], // End point (Tribeca)
      ],
    },
  ]);

  const [selectedRoute, setSelectedRoute] = useState<number | null>(null);

  // Get user's current location on component mount
  useEffect(() => {
    console.log("🚗 RouteOptimizer: Starting geolocation request...");

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(newLocation);
          console.log(
            "✅ RouteOptimizer: Current location detected:",
            newLocation
          );
        },
        (error) => {
          console.error("❌ RouteOptimizer: Error getting location:", error);
          console.error("Error code:", error.code, "Message:", error.message);

          // Fallback to NYC coordinates
          const fallbackLocation = { lat: 40.7128, lng: -74.006 };
          setCurrentLocation(fallbackLocation);
          console.log(
            "🏙️ RouteOptimizer: Using fallback location:",
            fallbackLocation
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000,
        }
      );
    } else {
      console.log(
        "❌ RouteOptimizer: Geolocation not supported, using fallback location"
      );
      setCurrentLocation({ lat: 40.7128, lng: -74.006 });
    }
  }, []);

  const getSafetyColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-100";
    if (score >= 60) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low":
        return "text-green-600";
      case "medium":
        return "text-yellow-600";
      case "high":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const handleRouteSearch = async () => {
    if (!destination.trim() || !currentLocation) return;

    setIsAnalyzing(true);
    setGeocodingError(null);
    console.log("Analyzing routes to:", destination);

    try {
      // First, geocode the destination address
      if (!mapboxToken) {
        setGeocodingError("Mapbox token required for route generation");
        setIsAnalyzing(false);
        return;
      }

      const destCoords = await geocodeAddress(destination, mapboxToken);

      if (!destCoords) {
        setGeocodingError(
          "Could not find the destination address. Please try a different address."
        );
        setIsAnalyzing(false);
        return;
      }

      setDestinationCoords(destCoords);

      // Calculate actual distance
      const actualDistance = calculateDistance(currentLocation, destCoords);

      // Generate new routes based on actual coordinates
      const newRoutes: RouteData[] = [
        {
          id: 1,
          name: "Safest Route",
          duration: `${Math.ceil(actualDistance * 15)} min`, // Assume 4 mph walking speed for safest route
          distance: `${(actualDistance * 1.2).toFixed(1)} mi`, // 20% longer for safety
          safetyScore: Math.floor(Math.random() * 15) + 85, // 85-100% for safest
          lighting: "excellent",
          crowdDensity: "high",
          crimeRisk: "low",
          features: [
            "Well-lit streets",
            "High foot traffic",
            "Security cameras",
          ],
          coordinates: generateRouteCoordinates(
            currentLocation,
            destCoords,
            "safest"
          ),
        },
        {
          id: 2,
          name: "Fastest Route",
          duration: `${Math.ceil(actualDistance * 12)} min`, // Assume 5 mph for fastest route
          distance: `${actualDistance.toFixed(1)} mi`,
          safetyScore: Math.floor(Math.random() * 20) + 60, // 60-80% for fastest
          lighting: "moderate",
          crowdDensity: "low",
          crimeRisk: "medium",
          features: ["Direct path", "Some dark areas", "Limited visibility"],
          coordinates: generateRouteCoordinates(
            currentLocation,
            destCoords,
            "fastest"
          ),
        },
        {
          id: 3,
          name: "Balanced Route",
          duration: `${Math.ceil(actualDistance * 13)} min`, // Balanced timing
          distance: `${(actualDistance * 1.1).toFixed(1)} mi`, // 10% longer for balance
          safetyScore: Math.floor(Math.random() * 15) + 75, // 75-90% for balanced
          lighting: "good",
          crowdDensity: "moderate",
          crimeRisk: "low",
          features: [
            "Good lighting",
            "Moderate traffic",
            "Emergency services nearby",
          ],
          coordinates: generateRouteCoordinates(
            currentLocation,
            destCoords,
            "balanced"
          ),
        },
      ];

      setRoutes(newRoutes);
      setIsAnalyzing(false);
    } catch (error) {
      console.error("Error generating routes:", error);
      setGeocodingError("Error generating routes. Please try again.");
      setIsAnalyzing(false);
    }
  };

  const handleRouteSelect = (routeId: number) => {
    setSelectedRoute(routeId);
    console.log("Selected route:", routeId);
    if (onRouteSelected) {
      onRouteSelected(routeId);
    }
  };

  const handleStartNavigation = () => {
    if (selectedRoute) {
      console.log("Starting navigation with route:", selectedRoute);
      // This would integrate with the map to show the route
    }
  };

  const initializeMapbox = () => {
    if (mapboxToken.trim()) {
      setShowTokenInput(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Route className="h-5 w-5" />
          AI Route Optimizer
        </CardTitle>
        <CardDescription>
          Get the safest route recommendations based on real-time data analysis
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mapbox Token Input */}
        {showTokenInput && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Enter Mapbox public token for route previews..."
                value={mapboxToken}
                onChange={(e) => setMapboxToken(e.target.value)}
                className="flex-1"
              />
              <Button onClick={initializeMapbox} disabled={!mapboxToken.trim()}>
                Enable Previews
              </Button>
            </div>
            <p className="text-xs text-blue-600">
              Get your free token at{" "}
              <a
                href="https://mapbox.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="underline"
              >
                mapbox.com
              </a>
            </p>
          </div>
        )}

        {/* Current Location Status */}
        {currentLocation && (
          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 text-green-700">
              <MapPin className="h-4 w-4" />
              <span className="text-sm font-medium">
                Current location detected
              </span>
            </div>
            <p className="text-xs text-green-600 mt-1">
              Lat: {currentLocation.lat.toFixed(4)}, Lng:{" "}
              {currentLocation.lng.toFixed(4)}
            </p>
          </div>
        )}

        {/* Destination Input */}
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              placeholder="Enter destination address..."
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              className="flex-1"
              onKeyPress={(e) => e.key === "Enter" && handleRouteSearch()}
            />
            <Button
              onClick={handleRouteSearch}
              className="px-6"
              disabled={
                isAnalyzing ||
                !destination.trim() ||
                !currentLocation ||
                !mapboxToken
              }
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                "Analyze Routes"
              )}
            </Button>
          </div>

          {/* Geocoding Error */}
          {geocodingError && (
            <div className="p-3 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-xs text-red-600 mt-1">{geocodingError}</p>
            </div>
          )}

          {/* Requirements Check */}
          {(!currentLocation || !mapboxToken) && (
            <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 text-yellow-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Requirements</span>
              </div>
              <div className="text-xs text-yellow-600 mt-1 space-y-1">
                {!currentLocation && <p>• Waiting for location access...</p>}
                {!mapboxToken && (
                  <p>• Mapbox token required for route generation</p>
                )}
              </div>
            </div>
          )}

          {/* Destination Coordinates (for debugging) */}
          {destinationCoords && (
            <div className="p-2 bg-gray-50 rounded border">
              <p className="text-xs text-gray-600">
                Destination: {destinationCoords.lat.toFixed(4)},{" "}
                {destinationCoords.lng.toFixed(4)}
              </p>
            </div>
          )}
        </div>

        {/* AI Analysis Status */}
        {isAnalyzing && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-blue-700">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm font-medium">
                AI analyzing routes to {destination}...
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Geocoding address, calculating distances, and analyzing safety
              factors
            </p>
          </div>
        )}

        {/* Route Options */}
        <div className="space-y-3">
          {routes.map((route) => (
            <div
              key={route.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRoute === route.id
                  ? "border-blue-500 bg-blue-50 shadow-md"
                  : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
              }`}
              onClick={() => handleRouteSelect(route.id)}
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    {route.name}
                    {selectedRoute === route.id && (
                      <Navigation className="h-4 w-4 text-blue-600" />
                    )}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span>{route.duration}</span>
                    <span>•</span>
                    <span>{route.distance}</span>
                  </div>
                </div>
                <Badge className={getSafetyColor(route.safetyScore)}>
                  {route.safetyScore}% Safe
                </Badge>
              </div>

              {/* Route Preview Map */}
              <div className="mb-3">
                <RoutePreviewMap route={route} mapboxToken={mapboxToken} />
              </div>

              {/* Environmental Factors */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="text-center">
                  <Eye className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Lighting</p>
                  <p className="text-sm font-medium capitalize">
                    {route.lighting}
                  </p>
                </div>
                <div className="text-center">
                  <Users className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Crowd Density</p>
                  <p className="text-sm font-medium capitalize">
                    {route.crowdDensity}
                  </p>
                </div>
                <div className="text-center">
                  <Shield className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p
                    className={`text-sm font-medium capitalize ${getRiskColor(
                      route.crimeRisk
                    )}`}
                  >
                    {route.crimeRisk}
                  </p>
                </div>
              </div>

              {/* Route Features */}
              <div className="space-y-1">
                <p className="text-xs text-gray-500 font-medium">
                  Key Features:
                </p>
                <div className="flex flex-wrap gap-1">
                  {route.features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Real-time Data Indicators */}
              {selectedRoute === route.id && (
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Live traffic: Good</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Weather: Clear</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {selectedRoute && (
          <Button className="w-full" size="lg" onClick={handleStartNavigation}>
            Start Navigation with Route {selectedRoute}
          </Button>
        )}

        {/* Real-time Safety Tips */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-medium text-sm text-purple-800 mb-2">
            AI Safety Tips
          </h4>
          <div className="space-y-1 text-xs text-purple-700">
            <p>• Share your route with trusted contacts</p>
            <p>• Stay alert and avoid distractions</p>
            <p>• Trust your instincts - change route if needed</p>
            <p>• Keep emergency contacts easily accessible</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouteOptimizer;
