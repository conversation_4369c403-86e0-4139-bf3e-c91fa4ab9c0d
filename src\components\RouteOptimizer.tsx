import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Shield,
  Eye,
  Users,
  Navigation,
  Route,
  AlertCircle,
} from "lucide-react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";

interface RouteOptimizerProps {
  onRouteSelected?: (routeId: number) => void;
}

interface Coordinates {
  lat: number;
  lng: number;
}

interface RouteData {
  id: number;
  name: string;
  duration: string;
  distance: string;
  safetyScore: number;
  lighting: string;
  crowdDensity: string;
  crimeRisk: string;
  features: string[];
  coordinates: [number, number][];
}

// Geocoding utility functions
const geocodeAddress = async (
  address: string,
  mapboxToken: string
): Promise<Coordinates | null> => {
  if (!mapboxToken || !address.trim()) return null;

  try {
    const response = await fetch(
      `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
        address
      )}.json?access_token=${mapboxToken}&limit=1`
    );

    if (!response.ok) throw new Error("Geocoding failed");

    const data = await response.json();

    if (data.features && data.features.length > 0) {
      const [lng, lat] = data.features[0].center;
      return { lat, lng };
    }

    return null;
  } catch (error) {
    console.error("Geocoding error:", error);
    return null;
  }
};

// Get real route using Mapbox Directions API
const getDirectionsRoute = async (
  start: Coordinates,
  end: Coordinates,
  routeType: "safest" | "fastest" | "balanced",
  mapboxToken: string
): Promise<{
  coordinates: [number, number][];
  duration: number;
  distance: number;
} | null> => {
  if (!mapboxToken) return null;

  try {
    // Different routing profiles for different route types
    const profile = routeType === "fastest" ? "driving" : "walking";
    const alternatives = routeType === "balanced" ? "true" : "false";

    const url = `https://api.mapbox.com/directions/v5/mapbox/${profile}/${start.lng},${start.lat};${end.lng},${end.lat}?alternatives=${alternatives}&geometries=geojson&steps=true&access_token=${mapboxToken}`;

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Directions API error: ${response.status}`);
    }

    const data = await response.json();

    if (data.routes && data.routes.length > 0) {
      let selectedRoute = data.routes[0];

      // For balanced route, try to pick a middle option if alternatives exist
      if (routeType === "balanced" && data.routes.length > 1) {
        selectedRoute = data.routes[Math.min(1, data.routes.length - 1)];
      }

      // For safest route, prefer the longest route (more conservative)
      if (routeType === "safest" && data.routes.length > 1) {
        selectedRoute = data.routes.reduce((longest, current) =>
          current.distance > longest.distance ? current : longest
        );
      }

      return {
        coordinates: selectedRoute.geometry.coordinates,
        duration: selectedRoute.duration, // in seconds
        distance: selectedRoute.distance, // in meters
      };
    }

    return null;
  } catch (error) {
    console.error("Directions API error:", error);
    return null;
  }
};

// Fallback: Generate approximate route coordinates for when API fails
const generateFallbackRouteCoordinates = (
  start: Coordinates,
  end: Coordinates,
  routeType: "safest" | "fastest" | "balanced"
): [number, number][] => {
  const startCoord: [number, number] = [start.lng, start.lat];
  const endCoord: [number, number] = [end.lng, end.lat];

  // Calculate the difference
  const deltaLng = end.lng - start.lng;
  const deltaLat = end.lat - start.lat;

  // Generate intermediate points based on route type
  const points: [number, number][] = [startCoord];

  const numPoints =
    routeType === "fastest" ? 3 : routeType === "safest" ? 8 : 5;

  for (let i = 1; i < numPoints; i++) {
    const progress = i / numPoints;

    // Add some variation based on route type
    let lngOffset = 0;
    let latOffset = 0;

    if (routeType === "safest") {
      // Safest route: more winding, avoids direct paths
      lngOffset = Math.sin(progress * Math.PI * 2) * Math.abs(deltaLng) * 0.1;
      latOffset =
        Math.cos(progress * Math.PI * 1.5) * Math.abs(deltaLat) * 0.05;
    } else if (routeType === "balanced") {
      // Balanced route: slight curves
      lngOffset = Math.sin(progress * Math.PI) * Math.abs(deltaLng) * 0.05;
      latOffset = Math.cos(progress * Math.PI) * Math.abs(deltaLat) * 0.03;
    }
    // Fastest route: minimal offset (almost direct)

    const lng = start.lng + deltaLng * progress + lngOffset;
    const lat = start.lat + deltaLat * progress + latOffset;

    points.push([lng, lat]);
  }

  points.push(endCoord);
  return points;
};

// Calculate distance between two coordinates (rough estimate)
const calculateDistance = (start: Coordinates, end: Coordinates): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = ((end.lat - start.lat) * Math.PI) / 180;
  const dLng = ((end.lng - start.lng) * Math.PI) / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((start.lat * Math.PI) / 180) *
      Math.cos((end.lat * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

const RoutePreviewMap: React.FC<{
  route: RouteData;
  mapboxToken?: string;
  isSelected?: boolean;
  onRouteClick?: () => void;
}> = ({ route, mapboxToken, isSelected = false, onRouteClick }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  useEffect(() => {
    if (!mapContainer.current || !mapboxToken) return;

    try {
      mapboxgl.accessToken = mapboxToken;

      // Ensure coordinates are properly formatted as [lng, lat]
      const startCoordinates = route.coordinates[0] as [number, number];

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: startCoordinates,
        zoom: 13,
        interactive: true, // Make maps interactive
      });

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

      map.current.on("load", () => {
        if (!map.current) return;
        setIsMapLoaded(true);

        // Add route line with hover effects
        map.current.addSource("route", {
          type: "geojson",
          data: {
            type: "Feature",
            properties: {
              routeId: route.id,
              routeName: route.name,
              safetyScore: route.safetyScore,
            },
            geometry: {
              type: "LineString",
              coordinates: route.coordinates,
            },
          },
        });

        // Main route line
        map.current.addLayer({
          id: "route",
          type: "line",
          source: "route",
          layout: {
            "line-join": "round",
            "line-cap": "round",
          },
          paint: {
            "line-color":
              route.safetyScore >= 80
                ? "#10b981"
                : route.safetyScore >= 60
                ? "#f59e0b"
                : "#ef4444",
            "line-width": isSelected ? 6 : 4,
            "line-opacity": 0.8,
          },
        });

        // Route outline for better visibility
        map.current.addLayer(
          {
            id: "route-outline",
            type: "line",
            source: "route",
            layout: {
              "line-join": "round",
              "line-cap": "round",
            },
            paint: {
              "line-color": "#ffffff",
              "line-width": isSelected ? 8 : 6,
              "line-opacity": 0.6,
            },
          },
          "route"
        );

        // Add start marker with popup
        const startMarker = new mapboxgl.Marker({
          color: "#3b82f6",
          scale: 1.2,
        })
          .setLngLat(startCoordinates)
          .setPopup(
            new mapboxgl.Popup().setHTML(`
              <div class="p-2">
                <h3 class="font-semibold text-sm text-blue-800">Start Location</h3>
                <p class="text-xs text-gray-600">Route: ${route.name}</p>
              </div>
            `)
          )
          .addTo(map.current);

        // Add end marker with popup
        const endCoordinates = route.coordinates[
          route.coordinates.length - 1
        ] as [number, number];
        const endMarker = new mapboxgl.Marker({
          color: "#ef4444",
          scale: 1.2,
        })
          .setLngLat(endCoordinates)
          .setPopup(
            new mapboxgl.Popup().setHTML(`
              <div class="p-2">
                <h3 class="font-semibold text-sm text-red-800">Destination</h3>
                <p class="text-xs text-gray-600">
                  ${route.duration} • ${route.distance}
                </p>
                <p class="text-xs text-gray-600">
                  Safety Score: ${route.safetyScore}%
                </p>
              </div>
            `)
          )
          .addTo(map.current);

        // Add click handler for route selection
        map.current.on("click", "route", (e) => {
          if (onRouteClick) {
            onRouteClick();
          }
        });

        // Add hover effects
        map.current.on("mouseenter", "route", () => {
          map.current!.getCanvas().style.cursor = "pointer";
        });

        map.current.on("mouseleave", "route", () => {
          map.current!.getCanvas().style.cursor = "";
        });

        // Fit bounds to show entire route
        const bounds = new mapboxgl.LngLatBounds();
        route.coordinates.forEach((coord: [number, number]) =>
          bounds.extend(coord)
        );
        map.current.fitBounds(bounds, { padding: 20 });
      });
    } catch (error) {
      console.error("Error initializing route preview map:", error);
    }

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [route, mapboxToken, isSelected]);

  if (!mapboxToken) {
    return (
      <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-sm text-gray-500">Map preview requires token</p>
      </div>
    );
  }

  return (
    <div className="relative">
      <div
        ref={mapContainer}
        className={`h-48 rounded border-2 transition-all duration-200 ${
          isSelected
            ? "border-blue-500 shadow-lg"
            : "border-gray-200 hover:border-gray-300"
        }`}
      />
      {!isMapLoaded && (
        <div className="absolute inset-0 bg-gray-100 rounded flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-xs text-gray-500">Loading route...</p>
          </div>
        </div>
      )}
    </div>
  );
};

const MainRouteMap: React.FC<{
  routes: RouteData[];
  selectedRoute: number | null;
  mapboxToken?: string;
  onRouteSelect: (routeId: number) => void;
  currentLocation?: Coordinates | null;
  destinationCoords?: Coordinates | null;
}> = ({
  routes,
  selectedRoute,
  mapboxToken,
  onRouteSelect,
  currentLocation,
  destinationCoords,
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);

  useEffect(() => {
    if (!mapContainer.current || !mapboxToken || routes.length === 0) return;

    try {
      mapboxgl.accessToken = mapboxToken;

      // Use current location or first route's start point
      const centerPoint = currentLocation
        ? ([currentLocation.lng, currentLocation.lat] as [number, number])
        : routes[0].coordinates[0];

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: centerPoint,
        zoom: 12,
        interactive: true,
      });

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), "top-right");
      map.current.addControl(new mapboxgl.FullscreenControl(), "top-right");

      map.current.on("load", () => {
        if (!map.current) return;
        setIsMapLoaded(true);

        // Add all routes to the map
        routes.forEach((route, index) => {
          const routeColor =
            route.safetyScore >= 80
              ? "#10b981"
              : route.safetyScore >= 60
              ? "#f59e0b"
              : "#ef4444";
          const isSelected = selectedRoute === route.id;

          // Add route source
          map.current!.addSource(`route-${route.id}`, {
            type: "geojson",
            data: {
              type: "Feature",
              properties: {
                routeId: route.id,
                routeName: route.name,
                safetyScore: route.safetyScore,
              },
              geometry: {
                type: "LineString",
                coordinates: route.coordinates,
              },
            },
          });

          // Add route outline
          map.current!.addLayer({
            id: `route-outline-${route.id}`,
            type: "line",
            source: `route-${route.id}`,
            layout: {
              "line-join": "round",
              "line-cap": "round",
            },
            paint: {
              "line-color": "#ffffff",
              "line-width": isSelected ? 10 : 8,
              "line-opacity": 0.8,
            },
          });

          // Add main route line
          map.current!.addLayer({
            id: `route-${route.id}`,
            type: "line",
            source: `route-${route.id}`,
            layout: {
              "line-join": "round",
              "line-cap": "round",
            },
            paint: {
              "line-color": routeColor,
              "line-width": isSelected ? 8 : 6,
              "line-opacity": isSelected ? 1.0 : 0.7,
            },
          });

          // Add click handler
          map.current!.on("click", `route-${route.id}`, () => {
            onRouteSelect(route.id);
          });

          // Add hover effects
          map.current!.on("mouseenter", `route-${route.id}`, () => {
            map.current!.getCanvas().style.cursor = "pointer";
          });

          map.current!.on("mouseleave", `route-${route.id}`, () => {
            map.current!.getCanvas().style.cursor = "";
          });
        });

        // Add start marker if current location exists
        if (currentLocation) {
          new mapboxgl.Marker({
            color: "#3b82f6",
            scale: 1.5,
          })
            .setLngLat([currentLocation.lng, currentLocation.lat])
            .setPopup(
              new mapboxgl.Popup().setHTML(`
                <div class="p-3">
                  <h3 class="font-semibold text-sm text-blue-800">Your Location</h3>
                  <p class="text-xs text-gray-600">Starting point for all routes</p>
                </div>
              `)
            )
            .addTo(map.current);
        }

        // Add destination marker if destination exists
        if (destinationCoords) {
          new mapboxgl.Marker({
            color: "#ef4444",
            scale: 1.5,
          })
            .setLngLat([destinationCoords.lng, destinationCoords.lat])
            .setPopup(
              new mapboxgl.Popup().setHTML(`
                <div class="p-3">
                  <h3 class="font-semibold text-sm text-red-800">Destination</h3>
                  <p class="text-xs text-gray-600">End point for all routes</p>
                </div>
              `)
            )
            .addTo(map.current);
        }

        // Fit bounds to show all routes
        const bounds = new mapboxgl.LngLatBounds();
        routes.forEach((route) => {
          route.coordinates.forEach((coord) => bounds.extend(coord));
        });
        map.current.fitBounds(bounds, { padding: 50 });
      });
    } catch (error) {
      console.error("Error initializing main route map:", error);
    }

    return () => {
      if (map.current) {
        map.current.remove();
      }
    };
  }, [routes, selectedRoute, mapboxToken, currentLocation, destinationCoords]);

  if (!mapboxToken) {
    return (
      <div className="h-96 bg-gray-100 rounded flex items-center justify-center">
        <p className="text-sm text-gray-500">Map requires Mapbox token</p>
      </div>
    );
  }

  if (routes.length === 0) {
    return (
      <div className="h-96 bg-gray-50 rounded border-2 border-dashed border-gray-200 flex items-center justify-center">
        <div className="text-center">
          <Route className="h-12 w-12 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500 font-medium">No routes to display</p>
          <p className="text-gray-400 text-sm">
            Enter a destination to see route options
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div
        ref={mapContainer}
        className="h-96 rounded border-2 border-gray-200"
      />
      {!isMapLoaded && (
        <div className="absolute inset-0 bg-gray-100 rounded flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Loading route map...</p>
          </div>
        </div>
      )}

      {/* Route Legend */}
      {isMapLoaded && routes.length > 0 && (
        <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3 max-w-xs">
          <h4 className="font-semibold text-sm mb-2">Route Options</h4>
          <div className="space-y-2">
            {routes.map((route) => (
              <div
                key={route.id}
                className={`flex items-center gap-2 text-xs cursor-pointer p-1 rounded ${
                  selectedRoute === route.id ? "bg-blue-50" : "hover:bg-gray-50"
                }`}
                onClick={() => onRouteSelect(route.id)}
              >
                <div
                  className="w-3 h-1 rounded"
                  style={{
                    backgroundColor:
                      route.safetyScore >= 80
                        ? "#10b981"
                        : route.safetyScore >= 60
                        ? "#f59e0b"
                        : "#ef4444",
                  }}
                />
                <span
                  className={selectedRoute === route.id ? "font-medium" : ""}
                >
                  {route.name}
                </span>
                <span className="text-gray-500">({route.duration})</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const RouteOptimizer: React.FC<RouteOptimizerProps> = ({ onRouteSelected }) => {
  const [destination, setDestination] = useState("");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [mapboxToken, setMapboxToken] = useState("");
  const [showTokenInput, setShowTokenInput] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<Coordinates | null>(
    null
  );
  const [destinationCoords, setDestinationCoords] =
    useState<Coordinates | null>(null);
  const [geocodingError, setGeocodingError] = useState<string | null>(null);
  const [routes, setRoutes] = useState<RouteData[]>([
    {
      id: 1,
      name: "Safest Route",
      duration: "18 min",
      distance: "2.1 mi",
      safetyScore: 92,
      lighting: "excellent",
      crowdDensity: "high",
      crimeRisk: "low",
      features: ["Well-lit streets", "High foot traffic", "Security cameras"],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.005, 40.7135], // Move east slightly
        [-74.0045, 40.7142], // Continue northeast
        [-74.004, 40.7148], // Well-lit Broadway area
        [-74.0035, 40.7155], // High foot traffic zone
        [-74.003, 40.7162], // Security camera area
        [-74.0025, 40.7168], // Continue through safe area
        [-74.002, 40.7175], // Approach destination
        [-74.0015, 40.7182], // Final safe approach
        [-74.001, 40.719], // End point (Tribeca)
      ],
    },
    {
      id: 2,
      name: "Fastest Route",
      duration: "12 min",
      distance: "1.8 mi",
      safetyScore: 67,
      lighting: "moderate",
      crowdDensity: "low",
      crimeRisk: "medium",
      features: ["Direct path", "Some dark areas", "Limited visibility"],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.0045, 40.714], // Direct northwest
        [-74.003, 40.7155], // Shortcut through less populated area
        [-74.002, 40.717], // Continue direct path
        [-74.001, 40.719], // End point (Tribeca) - most direct
      ],
    },
    {
      id: 3,
      name: "Balanced Route",
      duration: "15 min",
      distance: "2.0 mi",
      safetyScore: 78,
      lighting: "good",
      crowdDensity: "moderate",
      crimeRisk: "low",
      features: [
        "Good lighting",
        "Moderate traffic",
        "Emergency services nearby",
      ],
      coordinates: [
        [-74.006, 40.7128], // Start point (Financial District)
        [-74.0055, 40.7135], // Slight westward curve
        [-74.0048, 40.7145], // Balanced path through moderate area
        [-74.004, 40.7158], // Near emergency services
        [-74.0032, 40.717], // Moderate traffic zone
        [-74.0025, 40.718], // Continue balanced approach
        [-74.0018, 40.7188], // Final approach
        [-74.001, 40.719], // End point (Tribeca)
      ],
    },
  ]);

  const [selectedRoute, setSelectedRoute] = useState<number | null>(null);

  // Get user's current location on component mount
  useEffect(() => {
    console.log("🚗 RouteOptimizer: Starting geolocation request...");

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          setCurrentLocation(newLocation);
          console.log(
            "✅ RouteOptimizer: Current location detected:",
            newLocation
          );
        },
        (error) => {
          console.error("❌ RouteOptimizer: Error getting location:", error);
          console.error("Error code:", error.code, "Message:", error.message);

          // Fallback to NYC coordinates
          const fallbackLocation = { lat: 40.7128, lng: -74.006 };
          setCurrentLocation(fallbackLocation);
          console.log(
            "🏙️ RouteOptimizer: Using fallback location:",
            fallbackLocation
          );
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000,
        }
      );
    } else {
      console.log(
        "❌ RouteOptimizer: Geolocation not supported, using fallback location"
      );
      setCurrentLocation({ lat: 40.7128, lng: -74.006 });
    }
  }, []);

  const getSafetyColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-100";
    if (score >= 60) return "text-yellow-600 bg-yellow-100";
    return "text-red-600 bg-red-100";
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low":
        return "text-green-600";
      case "medium":
        return "text-yellow-600";
      case "high":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const handleRouteSearch = async () => {
    if (!destination.trim() || !currentLocation) return;

    setIsAnalyzing(true);
    setGeocodingError(null);
    console.log("Analyzing routes to:", destination);

    try {
      // First, geocode the destination address
      if (!mapboxToken) {
        setGeocodingError("Mapbox token required for route generation");
        setIsAnalyzing(false);
        return;
      }

      const destCoords = await geocodeAddress(destination, mapboxToken);

      if (!destCoords) {
        setGeocodingError(
          "Could not find the destination address. Please try a different address."
        );
        setIsAnalyzing(false);
        return;
      }

      setDestinationCoords(destCoords);

      // Calculate actual distance
      const actualDistance = calculateDistance(currentLocation, destCoords);

      // Generate new routes using real Mapbox Directions API
      console.log("🗺️ Generating routes using Mapbox Directions API...");

      const routeTypes: Array<"safest" | "fastest" | "balanced"> = [
        "safest",
        "fastest",
        "balanced",
      ];
      const newRoutes: RouteData[] = [];

      for (let i = 0; i < routeTypes.length; i++) {
        const routeType = routeTypes[i];
        console.log(`🛣️ Generating ${routeType} route...`);

        try {
          // Try to get real route from Mapbox Directions API
          const directionsResult = await getDirectionsRoute(
            currentLocation,
            destCoords,
            routeType,
            mapboxToken
          );

          let routeCoordinates: [number, number][];
          let duration: string;
          let distance: string;

          if (directionsResult) {
            // Use real route data
            routeCoordinates = directionsResult.coordinates;
            duration = `${Math.ceil(directionsResult.duration / 60)} min`;
            distance = `${(directionsResult.distance * 0.000621371).toFixed(
              1
            )} mi`; // Convert meters to miles
            console.log(`✅ Got real ${routeType} route from API`);
          } else {
            // Fallback to generated coordinates
            routeCoordinates = generateFallbackRouteCoordinates(
              currentLocation,
              destCoords,
              routeType
            );
            duration = `${Math.ceil(
              actualDistance *
                (routeType === "fastest"
                  ? 12
                  : routeType === "safest"
                  ? 15
                  : 13)
            )} min`;
            distance = `${(
              actualDistance *
              (routeType === "safest"
                ? 1.2
                : routeType === "balanced"
                ? 1.1
                : 1.0)
            ).toFixed(1)} mi`;
            console.log(`⚠️ Using fallback route for ${routeType}`);
          }

          // Create route data with appropriate characteristics
          const routeData: RouteData = {
            id: i + 1,
            name:
              routeType === "safest"
                ? "Safest Route"
                : routeType === "fastest"
                ? "Fastest Route"
                : "Balanced Route",
            duration,
            distance,
            safetyScore:
              routeType === "safest"
                ? Math.floor(Math.random() * 15) + 85
                : routeType === "fastest"
                ? Math.floor(Math.random() * 20) + 60
                : Math.floor(Math.random() * 15) + 75,
            lighting:
              routeType === "safest"
                ? "excellent"
                : routeType === "fastest"
                ? "moderate"
                : "good",
            crowdDensity:
              routeType === "safest"
                ? "high"
                : routeType === "fastest"
                ? "low"
                : "moderate",
            crimeRisk:
              routeType === "safest"
                ? "low"
                : routeType === "fastest"
                ? "medium"
                : "low",
            features:
              routeType === "safest"
                ? ["Well-lit streets", "High foot traffic", "Security cameras"]
                : routeType === "fastest"
                ? ["Direct path", "Some dark areas", "Limited visibility"]
                : [
                    "Good lighting",
                    "Moderate traffic",
                    "Emergency services nearby",
                  ],
            coordinates: routeCoordinates,
          };

          newRoutes.push(routeData);
        } catch (error) {
          console.error(`❌ Error generating ${routeType} route:`, error);

          // Create fallback route
          const fallbackRoute: RouteData = {
            id: i + 1,
            name:
              routeType === "safest"
                ? "Safest Route"
                : routeType === "fastest"
                ? "Fastest Route"
                : "Balanced Route",
            duration: `${Math.ceil(
              actualDistance *
                (routeType === "fastest"
                  ? 12
                  : routeType === "safest"
                  ? 15
                  : 13)
            )} min`,
            distance: `${(
              actualDistance *
              (routeType === "safest"
                ? 1.2
                : routeType === "balanced"
                ? 1.1
                : 1.0)
            ).toFixed(1)} mi`,
            safetyScore:
              routeType === "safest"
                ? Math.floor(Math.random() * 15) + 85
                : routeType === "fastest"
                ? Math.floor(Math.random() * 20) + 60
                : Math.floor(Math.random() * 15) + 75,
            lighting:
              routeType === "safest"
                ? "excellent"
                : routeType === "fastest"
                ? "moderate"
                : "good",
            crowdDensity:
              routeType === "safest"
                ? "high"
                : routeType === "fastest"
                ? "low"
                : "moderate",
            crimeRisk:
              routeType === "safest"
                ? "low"
                : routeType === "fastest"
                ? "medium"
                : "low",
            features:
              routeType === "safest"
                ? ["Well-lit streets", "High foot traffic", "Security cameras"]
                : routeType === "fastest"
                ? ["Direct path", "Some dark areas", "Limited visibility"]
                : [
                    "Good lighting",
                    "Moderate traffic",
                    "Emergency services nearby",
                  ],
            coordinates: generateFallbackRouteCoordinates(
              currentLocation,
              destCoords,
              routeType
            ),
          };

          newRoutes.push(fallbackRoute);
        }
      }

      setRoutes(newRoutes);
      setIsAnalyzing(false);
    } catch (error) {
      console.error("Error generating routes:", error);
      setGeocodingError("Error generating routes. Please try again.");
      setIsAnalyzing(false);
    }
  };

  const handleRouteSelect = (routeId: number) => {
    setSelectedRoute(routeId);
    console.log("Selected route:", routeId);
    if (onRouteSelected) {
      onRouteSelected(routeId);
    }
  };

  const handleStartNavigation = () => {
    if (selectedRoute) {
      console.log("Starting navigation with route:", selectedRoute);
      // This would integrate with the map to show the route
    }
  };

  const initializeMapbox = () => {
    if (mapboxToken.trim()) {
      setShowTokenInput(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Route className="h-5 w-5" />
          AI Route Optimizer
        </CardTitle>
        <CardDescription>
          Get the safest route recommendations based on real-time data analysis
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Mapbox Token Input */}
        {showTokenInput && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex gap-2 mb-2">
              <Input
                placeholder="Enter Mapbox public token for route previews..."
                value={mapboxToken}
                onChange={(e) => setMapboxToken(e.target.value)}
                className="flex-1"
              />
              <Button onClick={initializeMapbox} disabled={!mapboxToken.trim()}>
                Enable Previews
              </Button>
            </div>
            <p className="text-xs text-blue-600">
              Get your free token at{" "}
              <a
                href="https://mapbox.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="underline"
              >
                mapbox.com
              </a>
            </p>
          </div>
        )}

        {/* Current Location Status */}
        {currentLocation && (
          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 text-green-700">
              <MapPin className="h-4 w-4" />
              <span className="text-sm font-medium">
                Current location detected
              </span>
            </div>
            <p className="text-xs text-green-600 mt-1">
              Lat: {currentLocation.lat.toFixed(4)}, Lng:{" "}
              {currentLocation.lng.toFixed(4)}
            </p>
          </div>
        )}

        {/* Destination Input */}
        <div className="space-y-2">
          <div className="flex gap-2">
            <Input
              placeholder="Enter destination address..."
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              className="flex-1"
              onKeyPress={(e) => e.key === "Enter" && handleRouteSearch()}
            />
            <Button
              onClick={handleRouteSearch}
              className="px-6"
              disabled={
                isAnalyzing ||
                !destination.trim() ||
                !currentLocation ||
                !mapboxToken
              }
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                "Analyze Routes"
              )}
            </Button>
          </div>

          {/* Geocoding Error */}
          {geocodingError && (
            <div className="p-3 bg-red-50 rounded-lg border border-red-200">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Error</span>
              </div>
              <p className="text-xs text-red-600 mt-1">{geocodingError}</p>
            </div>
          )}

          {/* Requirements Check */}
          {(!currentLocation || !mapboxToken) && (
            <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <div className="flex items-center gap-2 text-yellow-700">
                <AlertCircle className="h-4 w-4" />
                <span className="text-sm font-medium">Requirements</span>
              </div>
              <div className="text-xs text-yellow-600 mt-1 space-y-1">
                {!currentLocation && <p>• Waiting for location access...</p>}
                {!mapboxToken && (
                  <p>• Mapbox token required for route generation</p>
                )}
              </div>
            </div>
          )}

          {/* Destination Coordinates (for debugging) */}
          {destinationCoords && (
            <div className="p-2 bg-gray-50 rounded border">
              <p className="text-xs text-gray-600">
                Destination: {destinationCoords.lat.toFixed(4)},{" "}
                {destinationCoords.lng.toFixed(4)}
              </p>
            </div>
          )}
        </div>

        {/* AI Analysis Status */}
        {isAnalyzing && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 text-blue-700">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm font-medium">
                AI analyzing routes to {destination}...
              </span>
            </div>
            <p className="text-xs text-blue-600 mt-1">
              Geocoding address, calculating distances, and analyzing safety
              factors
            </p>
          </div>
        )}

        {/* Main Interactive Route Map */}
        {routes.length > 0 && !isAnalyzing && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-lg">Route Overview</h3>
              <Badge variant="outline" className="text-xs">
                {routes.length} routes found
              </Badge>
            </div>
            <MainRouteMap
              routes={routes}
              selectedRoute={selectedRoute}
              mapboxToken={mapboxToken}
              onRouteSelect={handleRouteSelect}
              currentLocation={currentLocation}
              destinationCoords={destinationCoords}
            />
          </div>
        )}

        {/* Route Options */}
        <div className="space-y-3">
          {routes.map((route) => (
            <div
              key={route.id}
              className={`p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRoute === route.id
                  ? "border-blue-500 bg-blue-50 shadow-md"
                  : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
              }`}
              onClick={() => handleRouteSelect(route.id)}
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    {route.name}
                    {selectedRoute === route.id && (
                      <Navigation className="h-4 w-4 text-blue-600" />
                    )}
                  </h3>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <span>{route.duration}</span>
                    <span>•</span>
                    <span>{route.distance}</span>
                  </div>
                </div>
                <Badge className={getSafetyColor(route.safetyScore)}>
                  {route.safetyScore}% Safe
                </Badge>
              </div>

              {/* Route Preview Map */}
              <div className="mb-3">
                <RoutePreviewMap
                  route={route}
                  mapboxToken={mapboxToken}
                  isSelected={selectedRoute === route.id}
                  onRouteClick={() => handleRouteSelect(route.id)}
                />
              </div>

              {/* Environmental Factors */}
              <div className="grid grid-cols-3 gap-4 mb-3">
                <div className="text-center">
                  <Eye className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Lighting</p>
                  <p className="text-sm font-medium capitalize">
                    {route.lighting}
                  </p>
                </div>
                <div className="text-center">
                  <Users className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p className="text-xs text-gray-500">Crowd Density</p>
                  <p className="text-sm font-medium capitalize">
                    {route.crowdDensity}
                  </p>
                </div>
                <div className="text-center">
                  <Shield className="h-4 w-4 mx-auto mb-1 text-gray-600" />
                  <p
                    className={`text-sm font-medium capitalize ${getRiskColor(
                      route.crimeRisk
                    )}`}
                  >
                    {route.crimeRisk}
                  </p>
                </div>
              </div>

              {/* Route Features */}
              <div className="space-y-1">
                <p className="text-xs text-gray-500 font-medium">
                  Key Features:
                </p>
                <div className="flex flex-wrap gap-1">
                  {route.features.map((feature, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Real-time Data Indicators */}
              {selectedRoute === route.id && (
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Live traffic: Good</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span>Weather: Clear</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {selectedRoute && (
          <Button className="w-full" size="lg" onClick={handleStartNavigation}>
            Start Navigation with Route {selectedRoute}
          </Button>
        )}

        {/* Real-time Safety Tips */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-medium text-sm text-purple-800 mb-2">
            AI Safety Tips
          </h4>
          <div className="space-y-1 text-xs text-purple-700">
            <p>• Share your route with trusted contacts</p>
            <p>• Stay alert and avoid distractions</p>
            <p>• Trust your instincts - change route if needed</p>
            <p>• Keep emergency contacts easily accessible</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RouteOptimizer;
