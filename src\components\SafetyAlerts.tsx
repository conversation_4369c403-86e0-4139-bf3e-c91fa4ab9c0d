import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bell, AlertTriangle, MapPin, Eye, Shield } from 'lucide-react';

interface SafetyAlertsProps {
  alerts: number;
}

const SafetyAlerts: React.FC<SafetyAlertsProps> = ({ alerts }) => {
  const [alertList, setAlertList] = useState([
    {
      id: 1,
      type: 'area_alert',
      severity: 'medium',
      title: 'Increased Activity Detected',
      message: 'AI detected unusual crowd patterns in your area. Consider alternative routes.',
      location: '0.2 miles from your location',
      timestamp: '5 minutes ago',
      actionable: true
    },
    {
      id: 2,
      type: 'environmental',
      severity: 'low',
      title: 'Poor Lighting Ahead',
      message: 'Limited street lighting detected on your current route after 8 PM.',
      location: 'Main St & 5th Ave',
      timestamp: '12 minutes ago',
      actionable: true
    }
  ]);

  const [realtimeData, setRealtimeData] = useState({
    nearbyUsers: 23,
    lightingCondition: 'Good',
    crowdDensity: 'Moderate',
    weatherImpact: 'None'
  });

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setRealtimeData(prev => ({
        ...prev,
        nearbyUsers: Math.floor(Math.random() * 50) + 10,
        crowdDensity: Math.random() > 0.5 ? 'High' : 'Moderate'
      }));
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'outline';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'medium': return <Bell className="h-4 w-4 text-yellow-500" />;
      case 'low': return <Eye className="h-4 w-4 text-blue-500" />;
      default: return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const handleDismissAlert = (alertId: number) => {
    setAlertList(prev => prev.filter(alert => alert.id !== alertId));
  };

  const handleViewAlternatives = (alertId: number) => {
    console.log('Showing alternatives for alert:', alertId);
    // This would trigger route recalculation or show alternative options
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Safety Alerts & Real-time Data
        </CardTitle>
        <CardDescription>
          AI-powered alerts and environmental monitoring
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Real-time Environmental Data */}
        <div className="grid grid-cols-2 gap-3">
          <div className="p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2 mb-1">
              <Eye className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Lighting</span>
            </div>
            <p className="text-lg font-semibold text-green-700">{realtimeData.lightingCondition}</p>
          </div>
          
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-1">
              <MapPin className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Nearby Users</span>
            </div>
            <p className="text-lg font-semibold text-blue-700">{realtimeData.nearbyUsers}</p>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Active Alerts ({alertList.length})</h4>
          
          {alertList.length === 0 ? (
            <div className="text-center py-4 text-gray-500">
              <Shield className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <p className="text-sm">All clear! No active safety alerts.</p>
            </div>
          ) : (
            alertList.map((alert) => (
              <Alert key={alert.id} className="border-l-4 border-l-yellow-400">
                <div className="flex items-start gap-3">
                  {getSeverityIcon(alert.severity)}
                  <div className="flex-1 space-y-2">
                    <div className="flex items-start justify-between">
                      <div>
                        <h5 className="font-medium text-sm">{alert.title}</h5>
                        <AlertDescription className="text-xs mt-1">
                          {alert.message}
                        </AlertDescription>
                        <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {alert.location}
                          </span>
                          <span>{alert.timestamp}</span>
                        </div>
                      </div>
                      <Badge variant={getSeverityColor(alert.severity) as any}>
                        {alert.severity}
                      </Badge>
                    </div>
                    
                    {alert.actionable && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewAlternatives(alert.id)}
                          className="text-xs"
                        >
                          View Alternatives
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDismissAlert(alert.id)}
                          className="text-xs"
                        >
                          Dismiss
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </Alert>
            ))
          )}
        </div>

        {/* AI Insights */}
        <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="font-medium text-sm text-purple-800 mb-2">AI Safety Insights</h4>
          <div className="space-y-2 text-xs text-purple-700">
            <p>• Peak safety hours: 6 AM - 10 PM in your area</p>
            <p>• Crowd density is {realtimeData.crowdDensity.toLowerCase()} - good for visibility</p>
            <p>• Weather conditions favorable for outdoor activity</p>
            <p>• Emergency response time: ~4 minutes in your location</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SafetyAlerts;
