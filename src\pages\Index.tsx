
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, MapPin, Bell, Eye, Users, Phone } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const Index = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: <Shield className="h-8 w-8 text-blue-600" />,
      title: "AI Route Optimization",
      description: "Get the safest routes based on real-time crime data, lighting conditions, and crowd density analysis."
    },
    {
      icon: <Bell className="h-8 w-8 text-green-600" />,
      title: "Smart Alerts",
      description: "Receive intelligent notifications about potential safety risks in your area with predictive analytics."
    },
    {
      icon: <Eye className="h-8 w-8 text-purple-600" />,
      title: "Environmental Monitoring",
      description: "Real-time analysis of lighting, crowd density, and environmental factors affecting your safety."
    },
    {
      icon: <Phone className="h-8 w-8 text-red-600" />,
      title: "Discreet Emergency Features",
      description: "Multiple discreet ways to activate emergency alerts including voice commands and gesture recognition."
    },
    {
      icon: <Users className="h-8 w-8 text-orange-600" />,
      title: "Community Safety Network",
      description: "Connect with nearby users and share safety information to build a stronger community protection network."
    },
    {
      icon: <MapPin className="h-8 w-8 text-teal-600" />,
      title: "Real-time Location Sharing",
      description: "Smart location sharing with trusted contacts that activates automatically during emergencies."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-blue-600 rounded-full">
                <Shield className="h-12 w-12 text-white" />
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Sentinel Safe Paths
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Your AI-powered personal safety companion. Get intelligent route recommendations, 
              real-time safety alerts, and discreet emergency features to keep you safe wherever you go.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                onClick={() => navigate('/dashboard')}
                className="px-8 py-3 text-lg"
              >
                Launch Safety Dashboard
              </Button>
              <Button 
                size="lg" 
                variant="outline"
                className="px-8 py-3 text-lg"
              >
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Advanced AI Safety Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Powered by machine learning algorithms that analyze historical crime data, 
              environmental factors, and real-time conditions to keep you safer.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="flex justify-center mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center text-gray-600 leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How AI Enhances Your Safety
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our advanced AI algorithms work behind the scenes to provide you with 
              the most comprehensive safety experience possible.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Data Collection & Analysis</h3>
              <p className="text-gray-600">
                AI continuously analyzes crime statistics, user reports, lighting conditions, 
                and crowd density to build comprehensive safety models.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Predictive Risk Assessment</h3>
              <p className="text-gray-600">
                Machine learning algorithms predict potential risks based on historical patterns, 
                time of day, weather conditions, and current events.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold mb-4">Smart Recommendations</h3>
              <p className="text-gray-600">
                Receive personalized safety advice, optimal routes, and proactive alerts 
                tailored to your specific situation and preferences.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Start Your Safer Journey Today
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of users who trust Sentinel Safe Paths to keep them safe. 
            Experience the power of AI-driven personal safety.
          </p>
          <Button 
            size="lg" 
            variant="secondary"
            onClick={() => navigate('/dashboard')}
            className="px-8 py-3 text-lg bg-white text-blue-600 hover:bg-gray-100"
          >
            Get Started Now
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Index;
