# RouteOptimizer Component Testing Guide

## Overview
The RouteOptimizer component has been updated to use real geocoding and dynamic route generation based on user input destination addresses.

## Key Features Implemented

### 1. Real Geocoding
- Uses Mapbox Geocoding API to convert destination addresses to coordinates
- Handles geocoding errors gracefully with user-friendly error messages
- Validates addresses before generating routes

### 2. Dynamic Route Generation
- Generates three different route types: Safest, Fastest, and Balanced
- Uses actual start and destination coordinates
- Calculates realistic distances and travel times
- Creates route coordinates with appropriate variations for each route type

### 3. Current Location Detection
- Automatically detects user's current location using browser geolocation API
- Falls back to NYC coordinates if location access is denied
- Shows current location status to the user

### 4. Enhanced Error Handling
- Clear error messages for geocoding failures
- Requirements checking (location and Mapbox token)
- Visual feedback for all states (loading, error, success)

## Testing Steps

### Prerequisites
1. Get a free Mapbox public token from https://mapbox.com/
2. Ensure location access is enabled in your browser

### Test Cases

#### Test 1: Basic Functionality
1. Open the application
2. Navigate to the RouteOptimizer component
3. Enter your Mapbox token when prompted
4. Allow location access when prompted
5. Enter a destination address (e.g., "Times Square, New York")
6. Click "Analyze Routes"
7. Verify that three routes are generated with different characteristics

#### Test 2: Error Handling
1. Try entering an invalid address (e.g., "asdfghjkl")
2. Verify that an appropriate error message is displayed
3. Try using the component without a Mapbox token
4. Verify that requirements are clearly communicated

#### Test 3: Route Characteristics
1. Generate routes to a destination
2. Verify that:
   - Safest route has highest safety score (85-100%)
   - Fastest route has shortest duration
   - Balanced route has moderate characteristics
   - Route coordinates actually connect start and destination points

#### Test 4: Map Previews
1. With a valid Mapbox token, verify that route preview maps display correctly
2. Check that routes are visually different on the maps
3. Verify that start and end markers are placed correctly

## Expected Behavior

### Route Generation
- **Safest Route**: Longer path with higher safety score, more waypoints
- **Fastest Route**: Direct path with moderate safety score, fewer waypoints  
- **Balanced Route**: Compromise between safety and speed

### Distance Calculation
- Uses Haversine formula for accurate distance calculation
- Adjusts route distances based on route type (safest is 20% longer, balanced is 10% longer)

### Travel Time Estimation
- Safest: ~4 mph walking speed (15 min/mile)
- Fastest: ~5 mph walking speed (12 min/mile)
- Balanced: ~4.6 mph walking speed (13 min/mile)

## Known Limitations
1. Route generation is algorithmic, not based on actual road networks
2. Safety scores are simulated, not based on real crime data
3. Requires Mapbox token for full functionality
4. Requires location access for accurate route generation

## Future Enhancements
1. Integration with real routing APIs (Mapbox Directions API)
2. Real-time safety data integration
3. Historical crime data analysis
4. Traffic condition integration
5. Public transportation options
