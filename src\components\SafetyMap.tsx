import React, { useEffect, useRef, useState } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { MapPin, Shield, AlertTriangle, Key } from "lucide-react";

interface SafetyMapProps {
  currentLocation: { lat: number; lng: number } | null;
}

const SafetyMap: React.FC<SafetyMapProps> = ({ currentLocation }) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const currentLocationMarker = useRef<mapboxgl.Marker | null>(null);
  const [mapboxToken, setMapboxToken] = useState("");
  const [isMapInitialized, setIsMapInitialized] = useState(false);

  const [safetyZones, setSafetyZones] = useState<any[]>([]);
  const [incidents, setIncidents] = useState<any[]>([]);
  const [incidentMarkers, setIncidentMarkers] = useState<mapboxgl.Marker[]>([]);

  // Generate safety zones and incidents based on current location
  const generateLocationBasedData = (location: {
    lat: number;
    lng: number;
  }) => {
    console.log("🎯 SafetyMap: Generating location-based data for:", location);

    // Generate safety zones around the current location
    const newSafetyZones = [
      {
        id: 1,
        type: "safe",
        lat: location.lat + 0.003, // ~300m north
        lng: location.lng + 0.002, // ~200m east
        radius: 500,
        score: 95,
      },
      {
        id: 2,
        type: "moderate",
        lat: location.lat - 0.002, // ~200m south
        lng: location.lng + 0.004, // ~400m east
        radius: 300,
        score: 70,
      },
      {
        id: 3,
        type: "caution",
        lat: location.lat + 0.001, // ~100m north
        lng: location.lng - 0.003, // ~300m west
        radius: 200,
        score: 45,
      },
    ];

    // Generate incidents around the current location
    const newIncidents = [
      {
        id: 1,
        type: "theft",
        lat: location.lat - 0.001, // ~100m south
        lng: location.lng - 0.002, // ~200m west
        severity: "medium",
        time: "2 hours ago",
      },
      {
        id: 2,
        type: "harassment",
        lat: location.lat + 0.002, // ~200m north
        lng: location.lng + 0.001, // ~100m east
        severity: "low",
        time: "5 hours ago",
      },
    ];

    setSafetyZones(newSafetyZones);
    setIncidents(newIncidents);

    console.log("✅ SafetyMap: Generated safety zones:", newSafetyZones);
    console.log("✅ SafetyMap: Generated incidents:", newIncidents);
  };

  const initializeMap = () => {
    if (!mapContainer.current || !mapboxToken.trim()) return;

    try {
      mapboxgl.accessToken = mapboxToken;

      const initialLocation = currentLocation || { lat: 40.7128, lng: -74.006 };

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/streets-v12",
        center: [initialLocation.lng, initialLocation.lat],
        zoom: 13,
      });

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

      map.current.on("load", () => {
        console.log("Map loaded successfully");
        setIsMapInitialized(true);

        // Generate location-based data if we have a current location
        if (currentLocation) {
          generateLocationBasedData(currentLocation);
        }

        // Safety zones and incident markers will be added after data generation
        // Current location marker will be added via useEffect
      });
    } catch (error) {
      console.error("Error initializing map:", error);
    }
  };

  const addSafetyZones = () => {
    if (!map.current) return;

    // Clear existing safety zones first
    safetyZones.forEach((zone) => {
      if (map.current!.getSource(`zone-${zone.id}`)) {
        map.current!.removeLayer(`zone-${zone.id}`);
        map.current!.removeSource(`zone-${zone.id}`);
      }
    });

    safetyZones.forEach((zone) => {
      const color =
        zone.type === "safe"
          ? "#10b981"
          : zone.type === "moderate"
          ? "#f59e0b"
          : "#ef4444";

      // Add circle for safety zone
      map.current!.addSource(`zone-${zone.id}`, {
        type: "geojson",
        data: {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [zone.lng, zone.lat],
          },
          properties: {},
        },
      });

      map.current!.addLayer({
        id: `zone-${zone.id}`,
        type: "circle",
        source: `zone-${zone.id}`,
        paint: {
          "circle-radius": zone.radius / 10,
          "circle-color": color,
          "circle-opacity": 0.3,
          "circle-stroke-width": 2,
          "circle-stroke-color": color,
        },
      });
    });
  };

  const addIncidentMarkers = () => {
    if (!map.current) return;

    // Clear existing incident markers
    incidentMarkers.forEach((marker) => marker.remove());
    const newMarkers: mapboxgl.Marker[] = [];

    incidents.forEach((incident) => {
      const marker = new mapboxgl.Marker({
        color:
          incident.severity === "high"
            ? "#ef4444"
            : incident.severity === "medium"
            ? "#f59e0b"
            : "#6b7280",
      })
        .setLngLat([incident.lng, incident.lat])
        .setPopup(
          new mapboxgl.Popup().setHTML(`
        <div class="p-2">
          <h3 class="font-semibold text-sm">${incident.type}</h3>
          <p class="text-xs text-gray-600">${incident.time}</p>
          <span class="inline-block px-2 py-1 text-xs rounded ${
            incident.severity === "high"
              ? "bg-red-100 text-red-800"
              : incident.severity === "medium"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-gray-100 text-gray-800"
          }">${incident.severity}</span>
        </div>
      `)
        )
        .addTo(map.current!);

      newMarkers.push(marker);
    });

    setIncidentMarkers(newMarkers);
  };

  const addCurrentLocationMarker = () => {
    console.log("🎯 SafetyMap: addCurrentLocationMarker called");
    console.log("🗺️ Map current:", !!map.current);
    console.log("📍 Current location:", currentLocation);

    if (!map.current || !currentLocation) {
      console.log("❌ SafetyMap: Cannot add marker - missing map or location");
      return;
    }

    // Remove existing marker if it exists
    if (currentLocationMarker.current) {
      console.log("🗑️ SafetyMap: Removing existing marker");
      currentLocationMarker.current.remove();
    }

    console.log("📌 SafetyMap: Creating new marker at:", [
      currentLocation.lng,
      currentLocation.lat,
    ]);

    // Create new marker
    currentLocationMarker.current = new mapboxgl.Marker({
      color: "#3b82f6",
      scale: 1.2,
    })
      .setLngLat([currentLocation.lng, currentLocation.lat])
      .setPopup(
        new mapboxgl.Popup().setHTML(`
          <div class="p-3">
            <h3 class="font-semibold text-sm text-blue-800">Your Current Location</h3>
            <p class="text-xs text-gray-600 mt-1">
              Lat: ${currentLocation.lat.toFixed(4)}<br>
              Lng: ${currentLocation.lng.toFixed(4)}
            </p>
          </div>
        `)
      )
      .addTo(map.current);

    // Center map on current location
    console.log("🎯 SafetyMap: Flying to location:", [
      currentLocation.lng,
      currentLocation.lat,
    ]);
    map.current.flyTo({
      center: [currentLocation.lng, currentLocation.lat],
      zoom: 14,
      duration: 1000,
    });

    console.log("✅ SafetyMap: Current location marker added successfully");
  };

  const getZoneColor = (type: string) => {
    switch (type) {
      case "safe":
        return "bg-green-100 border-green-300";
      case "moderate":
        return "bg-yellow-100 border-yellow-300";
      case "caution":
        return "bg-red-100 border-red-300";
      default:
        return "bg-gray-100 border-gray-300";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "high":
        return "destructive";
      case "medium":
        return "outline";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  // Handle current location updates
  useEffect(() => {
    console.log("🗺️ SafetyMap: Location update effect triggered");
    console.log("📍 Current location:", currentLocation);
    console.log("🗺️ Map initialized:", isMapInitialized);

    if (isMapInitialized && currentLocation) {
      console.log("✅ SafetyMap: Adding current location marker");
      addCurrentLocationMarker();

      // Generate location-based data if we don't have any yet
      if (safetyZones.length === 0) {
        generateLocationBasedData(currentLocation);
      }
    } else {
      if (!isMapInitialized) {
        console.log("⏳ SafetyMap: Waiting for map to initialize");
      }
      if (!currentLocation) {
        console.log("⏳ SafetyMap: Waiting for current location");
      }
    }
  }, [currentLocation, isMapInitialized]);

  // Handle safety zones updates
  useEffect(() => {
    if (isMapInitialized && safetyZones.length > 0) {
      console.log("🛡️ SafetyMap: Adding safety zones to map");
      addSafetyZones();
    }
  }, [safetyZones, isMapInitialized]);

  // Handle incidents updates
  useEffect(() => {
    if (isMapInitialized && incidents.length > 0) {
      console.log("⚠️ SafetyMap: Adding incident markers to map");
      addIncidentMarkers();
    }
  }, [incidents, isMapInitialized]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentLocationMarker.current) {
        currentLocationMarker.current.remove();
      }
      incidentMarkers.forEach((marker) => marker.remove());
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  return (
    <div className="space-y-4">
      {/* Current Location Status */}
      {currentLocation && (
        <div className="p-3 bg-green-50 rounded-lg border border-green-200">
          <div className="flex items-center gap-2 text-green-700">
            <MapPin className="h-4 w-4" />
            <span className="text-sm font-medium">
              Current location detected
            </span>
          </div>
          <p className="text-xs text-green-600 mt-1">
            Your location will be shown on the map with a blue marker
          </p>
        </div>
      )}

      {/* Mapbox Token Input (if not connected to Supabase) */}
      {!isMapInitialized && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Key className="h-5 w-5" />
              Mapbox Configuration
            </CardTitle>
            <CardDescription>
              Enter your Mapbox public token to enable the interactive safety
              map
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Input
                placeholder="Enter your Mapbox public token..."
                value={mapboxToken}
                onChange={(e) => setMapboxToken(e.target.value)}
                className="flex-1"
              />
              <Button onClick={initializeMap} disabled={!mapboxToken.trim()}>
                Initialize Map
              </Button>
            </div>
            <p className="text-xs text-blue-600 mt-2">
              Get your free token at{" "}
              <a
                href="https://mapbox.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="underline"
              >
                mapbox.com
              </a>
            </p>
          </CardContent>
        </Card>
      )}

      {/* Interactive Map */}
      <div
        ref={mapContainer}
        className="h-80 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border-2 border-dashed border-blue-200 flex items-center justify-center relative overflow-hidden"
        style={{ display: isMapInitialized ? "block" : "flex" }}
      >
        {!isMapInitialized && (
          <div className="text-center">
            <MapPin className="h-12 w-12 text-blue-400 mx-auto mb-2" />
            <p className="text-blue-600 font-medium">Interactive Safety Map</p>
            <p className="text-blue-500 text-sm">
              Configure Mapbox token to enable
            </p>
          </div>
        )}
      </div>

      {/* Safety Zone Legend */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
        {safetyZones.map((zone) => (
          <div
            key={zone.id}
            className={`p-3 rounded-lg border ${getZoneColor(zone.type)}`}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                <span className="text-sm font-medium capitalize">
                  {zone.type}
                </span>
              </div>
              <Badge variant="outline">{zone.score}%</Badge>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Incidents */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Recent Incidents
          </CardTitle>
          <CardDescription>
            AI-analyzed safety reports in your area
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {incidents.map((incident) => (
              <div
                key={incident.id}
                className="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium capitalize">
                      {incident.type}
                    </p>
                    <p className="text-xs text-gray-500">{incident.time}</p>
                  </div>
                </div>
                <Badge variant={getSeverityColor(incident.severity) as any}>
                  {incident.severity}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SafetyMap;
